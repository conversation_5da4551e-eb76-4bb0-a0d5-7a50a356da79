import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:mobile/services/class_service.dart';
import 'package:mobile/services/auth_service.dart';
import 'package:mobile/widgets/booking_card.dart';

class MyBookingsScreen extends StatefulWidget {
  const MyBookingsScreen({super.key});

  @override
  State<MyBookingsScreen> createState() => _MyBookingsScreenState();
}

class _MyBookingsScreenState extends State<MyBookingsScreen>
    with SingleTickerProviderStateMixin {
  late final ClassService _classService;
  late TabController _tabController;
  List<Map<String, dynamic>> _userBookings = [];
  List<Map<String, dynamic>> _classInstances = [];
  bool _isLoading = true;
  String? _errorMessage;
  final ScrollController _scrollController = ScrollController();
  bool _showScrollToTop = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final authService = Provider.of<AuthService>(context, listen: false);
      _classService = ClassService(authService);
      _loadData();
    });
    
    // Listen to scroll events
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.offset > 200 && !_showScrollToTop) {
      setState(() {
        _showScrollToTop = true;
      });
    } else if (_scrollController.offset <= 200 && _showScrollToTop) {
      setState(() {
        _showScrollToTop = false;
      });
    }
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final bookingsFuture = _classService.getUserBookings();
      final instancesFuture = _classService.getAllClassInstances();
      
      final results = await Future.wait([bookingsFuture, instancesFuture]);
      
      setState(() {
        _userBookings = results[0];
        _classInstances = results[1];
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to load bookings: $e';
        _isLoading = false;
      });
    }
  }

  List<Map<String, dynamic>> get _currentBookings {
    final now = DateTime.now();
    return _userBookings.where((booking) {
      final classInstance = _getClassInstance(booking['classId']);
      if (classInstance == null) return false;
      
      final classDate = DateTime.parse(classInstance['date']);
      final startTime = _parseTimeString(classInstance['startTime']);
      if (startTime == null) return false;
      
      final classDateTime = DateTime(
        classDate.year,
        classDate.month,
        classDate.day,
        startTime.hour,
        startTime.minute,
      );
      
      return classDateTime.isAfter(now);
    }).toList()
      ..sort((a, b) {
        final aInstance = _getClassInstance(a['classId']);
        final bInstance = _getClassInstance(b['classId']);
        if (aInstance == null || bInstance == null) return 0;
        
        final aDate = DateTime.parse(aInstance['date']);
        final bDate = DateTime.parse(bInstance['date']);
        return aDate.compareTo(bDate);
      });
  }

  List<Map<String, dynamic>> get _pastBookings {
    final now = DateTime.now();
    return _userBookings.where((booking) {
      final classInstance = _getClassInstance(booking['classId']);
      if (classInstance == null) return false;
      
      final classDate = DateTime.parse(classInstance['date']);
      final startTime = _parseTimeString(classInstance['startTime']);
      if (startTime == null) return false;
      
      final classDateTime = DateTime(
        classDate.year,
        classDate.month,
        classDate.day,
        startTime.hour,
        startTime.minute,
      );
      
      return classDateTime.isBefore(now);
    }).toList()
      ..sort((a, b) {
        final aInstance = _getClassInstance(a['classId']);
        final bInstance = _getClassInstance(b['classId']);
        if (aInstance == null || bInstance == null) return 0;
        
        final aDate = DateTime.parse(aInstance['date']);
        final bDate = DateTime.parse(bInstance['date']);
        return bDate.compareTo(aDate); // Reverse order for past bookings
      });
  }

  DateTime? _parseTimeString(dynamic timeValue) {
    if (timeValue is DateTime) {
      return timeValue;
    }
    if (timeValue is String) {
      try {
        // Try parsing as ISO8601 string first
        return DateTime.parse(timeValue);
      } catch (e) {
        try {
          // Try parsing as time string (HH:mm)
          final parts = timeValue.split(':');
          if (parts.length == 2) {
            final hour = int.parse(parts[0]);
            final minute = int.parse(parts[1]);
            return DateTime(2024, 1, 1, hour, minute); // Use arbitrary date
          }
        } catch (e) {
          // Ignore parsing errors
        }
      }
    }
    return null;
  }

  Map<String, dynamic>? _getClassInstance(String classId) {
    try {
      return _classInstances.firstWhere((instance) => instance['id'] == classId);
    } catch (e) {
      return null;
    }
  }

  void _scrollToTop() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        0,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('My Bookings'),
        backgroundColor: theme.colorScheme.inversePrimary,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.schedule, size: 18),
                  const SizedBox(width: 8),
                  Text('Current (${_currentBookings.length})'),
                ],
              ),
            ),
            Tab(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.history, size: 18),
                  const SizedBox(width: 8),
                  Text('Past (${_pastBookings.length})'),
                ],
              ),
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadData,
        child: TabBarView(
          controller: _tabController,
          children: [
            _buildBookingsList(_currentBookings, 'current'),
            _buildBookingsList(_pastBookings, 'past'),
          ],
        ),
      ),
      floatingActionButton: _showScrollToTop
          ? FloatingActionButton(
              onPressed: _scrollToTop,
              backgroundColor: theme.colorScheme.primary,
              foregroundColor: theme.colorScheme.onPrimary,
              child: const Icon(Icons.keyboard_arrow_up),
            )
          : null,
    );
  }

  Widget _buildBookingsList(List<Map<String, dynamic>> bookings, String type) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Error loading bookings',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage!,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadData,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (bookings.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              type == 'current' ? Icons.schedule_outlined : Icons.history_outlined,
              size: 64,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.4),
            ),
            const SizedBox(height: 16),
            Text(
              type == 'current' ? 'No upcoming bookings' : 'No past bookings',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              type == 'current' 
                  ? 'Book a class to see it here'
                  : 'Your completed classes will appear here',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
              ),
              textAlign: TextAlign.center,
            ),

          ],
        ),
      );
    }

    // Build list with date separators
    List<Widget> items = [];
    String? lastDate;
    for (int i = 0; i < bookings.length; i++) {
      final booking = bookings[i];
      final classInstance = _getClassInstance(booking['classId']);
      if (classInstance == null) continue;
      final dateStr = classInstance['date'];
      if (lastDate != dateStr) {
        // Add a separator with the date label
        final date = DateTime.tryParse(dateStr);
        items.add(Padding(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          child: Row(
            children: [
              Expanded(
                child: Divider(
                  thickness: 1.2,
                  color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
                  endIndent: 8,
                ),
              ),
              Text(
                date != null
                    ? '${_weekdayName(date.weekday)}, ${date.day} ${_monthName(date.month)}'
                    : dateStr,
                style: Theme.of(context).textTheme.labelMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                      fontWeight: FontWeight.w600,
                    ),
              ),
              Expanded(
                child: Divider(
                  thickness: 1.2,
                  color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
                  indent: 8,
                ),
              ),
            ],
          ),
        ));
        lastDate = dateStr;
      }
      items.add(BookingCard(
        classInstance: classInstance,
        isPast: type == 'past',
      ));
    }

    return ListView(
      controller: _scrollController,
      physics: const BouncingScrollPhysics(),
      padding: const EdgeInsets.symmetric(vertical: 8),
      children: items,
    );
  }

  String _weekdayName(int weekday) {
    const names = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    return names[(weekday - 1) % 7];
  }

  String _monthName(int month) {
    const months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return months[(month - 1) % 12];
  }
} 