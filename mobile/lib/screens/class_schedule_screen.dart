import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:mobile/models/class_model.dart';
import 'package:mobile/services/class_service.dart';
import 'package:mobile/services/auth_service.dart';
import 'package:mobile/widgets/class_card.dart';
import 'package:intl/intl.dart';

class ClassScheduleScreen extends StatefulWidget {
  const ClassScheduleScreen({super.key});

  @override
  State<ClassScheduleScreen> createState() => _ClassScheduleScreenState();
}

class _ClassScheduleScreenState extends State<ClassScheduleScreen> {
  late final ClassService _classService;
  DateTime _selectedDate = DateTime.now();
  String? _selectedType;
  List<String> _availableTypes = [];
  List<ClassInstance> _classInstances = [];
  List<Map<String, dynamic>> _userBookings = [];
  bool _isLoading = true;
  String? _errorMessage;
  final ScrollController _scrollController = ScrollController();
  bool _showScrollToTop = false;
  DateTime _currentWeek = DateTime.now(); // Track current week
  final ScrollController _dayBarController = ScrollController();
  
  // Add loading state for booking operations
  Set<String> _loadingBookings = {}; // Track which classes are being booked/canceled

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final authService = Provider.of<AuthService>(context, listen: false);
      _classService = ClassService(authService);
      _selectedDate = DateTime.now();
      _currentWeek = _getMonday(DateTime.now());
      _loadData();
      _autoScrollToSelected();
    });
    
    // Listen to scroll events
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _dayBarController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.offset > 200 && !_showScrollToTop) {
      setState(() {
        _showScrollToTop = true;
      });
    } else if (_scrollController.offset <= 200 && _showScrollToTop) {
      setState(() {
        _showScrollToTop = false;
      });
    }
  }

  // Get the Monday of a given date
  DateTime _getMonday(DateTime date) {
    final monday = date.subtract(Duration(days: date.weekday - 1));
    return DateTime(monday.year, monday.month, monday.day);
  }

  // Get the Sunday of a given week
  DateTime _getSunday(DateTime monday) {
    return monday.add(const Duration(days: 6));
  }

  // Check if we're viewing the current week
  bool get _isCurrentWeek {
    final now = DateTime.now();
    final currentMonday = now.subtract(Duration(days: now.weekday - 1));
    final currentSunday = currentMonday.add(const Duration(days: 6));
    
    return _getMonday(_selectedDate).isAtSameMomentAs(currentMonday) &&
           _getSunday(_getMonday(_selectedDate)).isAtSameMomentAs(currentSunday);
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final monday = _getMonday(_selectedDate);
      final sunday = _getSunday(monday);
      
      final instancesFuture = _classService.getClassInstances(startDate: monday, endDate: sunday);
      final bookingsFuture = _classService.getUserBookings();
      
      final results = await Future.wait([instancesFuture, bookingsFuture]);

      final classInstances = results[0] as List<ClassInstance>;
      final types = classInstances.map((instance) => instance.type).toSet().toList();
      _availableTypes = types;
      
      setState(() {
        _availableTypes = types;
        _classInstances = classInstances;
        _userBookings = results[1] as List<Map<String, dynamic>>;
        _currentWeek = monday;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to load classes: $e';
        _isLoading = false;
      });
    }
  }

  List<ClassInstance> get _filteredClassInstances {
    return _classInstances.where((instance) {
      // Filter by day (convert instance date to day of week)
      final instanceDate = DateTime.parse(instance.date);
      if (!(instanceDate.year == _selectedDate.year && instanceDate.month == _selectedDate.month && instanceDate.day == _selectedDate.day)) return false;
      
      // Filter by type if selected
      if (_selectedType != null && instance.type != _selectedType) return false;
      
      // Only show scheduled instances
      return instance.status == 'scheduled';
    }).toList()
      ..sort((a, b) => a.startTime.compareTo(b.startTime));
  }

  void _scrollToTop() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        0,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }
  }

  void _centerSelectedDate(int index) {
    if (_dayBarController.hasClients) {
      // Calculate the position to center the selected date
      final screenWidth = MediaQuery.of(context).size.width;
      final itemWidth = 74.0; // 70 width + 4 margin (2px on each side)
      final listPadding = 16.0; // Horizontal padding of the ListView
      
      // Calculate the center position of the selected item
      final itemCenter = listPadding + (index * itemWidth) + (itemWidth / 2);
      final screenCenter = screenWidth / 2;
      final scrollOffset = itemCenter - screenCenter;
      
      // Ensure we don't scroll beyond the bounds
      final maxScroll = _dayBarController.position.maxScrollExtent;
      final finalOffset = scrollOffset.clamp(0.0, maxScroll);
      
      _dayBarController.animateTo(
        finalOffset,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _autoScrollToSelected() {
    final now = DateTime.now();
    final currentWeekMonday = now.subtract(Duration(days: now.weekday - 1));
    final selectedIndex = _selectedDate.difference(currentWeekMonday).inDays;
    _dayBarController.animateTo(
      (selectedIndex * 72.0).toDouble(),
      duration: const Duration(milliseconds: 400),
      curve: Curves.easeInOut,
    );
  }

  bool _isClassBooked(ClassInstance classInstance) {
    // If this class is currently being booked, show it as booked
    if (_loadingBookings.contains('${classInstance.id}_booking')) {
      return true;
    }
    // If this class is currently being canceled, show it as not booked
    if (_loadingBookings.contains('${classInstance.id}_canceling')) {
      return false;
    }
    return _userBookings.any((booking) => booking['classId'] == classInstance.id);
  }

  Map<String, dynamic>? _getBookingForClass(ClassInstance classInstance) {
    try {
      return _userBookings.firstWhere((booking) => booking['classId'] == classInstance.id);
    } catch (e) {
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Class Schedule'),
        backgroundColor: theme.colorScheme.inversePrimary,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadData,
        child: Column(
          children: [
            // Day selector
            _buildDaySelector(),
            
            // Classes list
            Expanded(
              child: _buildClassesList(),
            ),
          ],
        ),
      ),
      floatingActionButton: (_showScrollToTop || _filteredClassInstances.length > 5)
          ? FloatingActionButton(
              onPressed: _scrollToTop,
              backgroundColor: theme.colorScheme.primary,
              foregroundColor: theme.colorScheme.onPrimary,
              child: const Icon(Icons.keyboard_arrow_up),
            )
          : null,
    );
  }

  Widget _buildDaySelector() {
    final theme = Theme.of(context);
    final now = DateTime.now();
    
    // Calculate the Monday of current week
    final currentWeekMonday = now.subtract(Duration(days: now.weekday - 1));
    
    // Calculate the Sunday of next week (end of next week)
    final nextWeekSunday = currentWeekMonday.add(const Duration(days: 13)); // 7 + 6 = 13 days
    
    // Generate days from current week Monday to next week Sunday (14 days total)
    final days = List.generate(14, (i) => currentWeekMonday.add(Duration(days: i)));

    return Container(
      height: 80,
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: ListView.builder(
        controller: _dayBarController,
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: days.length,
        itemBuilder: (context, index) {
          final dayDate = days[index];
          final dayName = DateFormat('E').format(dayDate);
          final isSelected = dayDate.year == _selectedDate.year && dayDate.month == _selectedDate.month && dayDate.day == _selectedDate.day;
          final isToday = dayDate.year == now.year && dayDate.month == now.month && dayDate.day == now.day;

          return GestureDetector(
            onTap: () {
              setState(() {
                _selectedDate = dayDate;
                _currentWeek = _getMonday(dayDate);
              });
              _loadData();
              // Center the selected date in the day bar
              _centerSelectedDate(index);
              _scrollToTop();
            },
            child: Container(
              width: 70,
              margin: const EdgeInsets.symmetric(horizontal: 2),
              decoration: BoxDecoration(
                color: isSelected
                    ? theme.colorScheme.primary
                    : isToday
                        ? theme.colorScheme.primaryContainer
                        : theme.colorScheme.surface,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: isToday
                      ? theme.colorScheme.primary
                      : theme.colorScheme.outline.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    dayName,
                    style: theme.textTheme.labelSmall?.copyWith(
                      color: isSelected
                          ? theme.colorScheme.onPrimary
                          : isToday
                              ? theme.colorScheme.primary
                              : theme.colorScheme.onSurface,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    dayDate.day.toString(),
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: isSelected
                          ? theme.colorScheme.onPrimary
                          : isToday
                              ? theme.colorScheme.primary
                              : theme.colorScheme.onSurface,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }



  Widget _buildClassesList() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Error loading classes',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage!,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadData,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    final filteredInstances = _filteredClassInstances;

    if (filteredInstances.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _selectedType != null ? Icons.filter_list : Icons.fitness_center_outlined,
              size: 64,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.4),
            ),
            const SizedBox(height: 16),
            Text(
              _selectedType != null ? 'No ${_selectedType} classes' : 'No classes scheduled',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              _selectedType != null 
                  ? 'Try selecting a different type or date'
                  : 'Check back later for new classes',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
              ),
              textAlign: TextAlign.center,
            ),
            if (_selectedType != null) ...[
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _selectedType = null;
                  });
                },
                child: const Text('Clear Filter'),
              ),
            ],
          ],
        ),
      );
    }

    return ListView.builder(
      controller: _scrollController,
      physics: const BouncingScrollPhysics(),
      padding: const EdgeInsets.symmetric(vertical: 8),
      itemCount: filteredInstances.length,
      itemBuilder: (context, index) {
        final classInstance = filteredInstances[index];
        return ClassCard(
          classInstance: classInstance,
          isBooked: _isClassBooked(classInstance),
          onTap: () => _showClassDetails(classInstance),
        );
      },
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter Classes'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('All Classes'),
              leading: Radio<String?>(
                value: null,
                groupValue: _selectedType,
                onChanged: (value) {
                  setState(() {
                    _selectedType = value;
                  });
                  Navigator.pop(context);
                },
              ),
            ),
            ..._availableTypes.map((type) => ListTile(
              title: Text(type),
              leading: Radio<String?>(
                value: type,
                groupValue: _selectedType,
                onChanged: (value) {
                  setState(() {
                    _selectedType = value;
                  });
                  Navigator.pop(context);
                },
              ),
            )),
          ],
        ),
      ),
    );
  }

  void _showClassDetails(ClassInstance classInstance) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.6,
        minChildSize: 0.4,
        maxChildSize: 0.9,
        expand: false,
        builder: (context, scrollController) => Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Handle bar
              Center(
                child: Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),
              const SizedBox(height: 20),
              
              // Class title
              Text(
                classInstance.name,
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              
              // Class type
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: _getTypeColor(classInstance.type),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  classInstance.type,
                  style: Theme.of(context).textTheme.labelMedium?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(height: 20),
              
              // Class details
              Expanded(
                child: ListView(
                  controller: scrollController,
                  children: [
                    _buildDetailRow(
                      Icons.access_time,
                      'Time',
                      '${DateFormat('HH:mm').format(classInstance.startTime)} - ${DateFormat('HH:mm').format(classInstance.endTime)}',
                    ),
                    _buildDetailRow(
                      Icons.person,
                      'Coach',
                      classInstance.coach,
                    ),
                    _buildDetailRow(
                      Icons.calendar_today,
                      'Date',
                      DateFormat('EEEE, MMM dd, yyyy').format(DateTime.parse(classInstance.date)),
                    ),
                    _buildDetailRow(
                      Icons.people,
                      'Capacity',
                      '${classInstance.enrolledCount}/${classInstance.maxCapacity} students',
                    ),
                    _buildDetailRow(
                      Icons.info,
                      'Status',
                      classInstance.status.toUpperCase(),
                    ),
                  ],
                ),
              ),
              
              // Action buttons
              const SizedBox(height: 20),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.pop(context),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text('Close'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _isClassBooked(classInstance)
                        ? ElevatedButton(
                            onPressed: () => _cancelClass(classInstance),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Theme.of(context).colorScheme.error,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: const Text('Cancel Class'),
                          )
                        : ElevatedButton(
                            onPressed: classInstance.enrolledCount < classInstance.maxCapacity
                                ? () => _bookClass(classInstance)
                                : null,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Theme.of(context).colorScheme.primary,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: const Text('Book Class'),
                          ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(
            icon,
            size: 20,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(width: 12),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Theme.of(context).textTheme.labelSmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                ),
              ),
              Text(
                value,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _bookClass(ClassInstance classInstance) async {
    Navigator.pop(context);
    
    // Set loading state for this class
    setState(() {
      _loadingBookings.add('${classInstance.id}_booking');
    });
    
    // Show loading snackbar
    final loadingSnackBar = SnackBar(
      content: Row(
        children: [
          const SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          ),
          const SizedBox(width: 16),
          Text('Booking ${classInstance.name}...'),
        ],
      ),
      backgroundColor: Theme.of(context).colorScheme.primary,
      duration: const Duration(seconds: 10), // Longer duration to cover API call
    );
    
    ScaffoldMessenger.of(context).showSnackBar(loadingSnackBar);

    try {
      final success = await _classService.bookClass(classInstance.id);
      
      // Hide loading snackbar
      ScaffoldMessenger.of(context).hideCurrentSnackBar();
      
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Successfully booked ${classInstance.name}!'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 3),
          ),
        );
        // Refresh the data to show updated availability
        _loadData();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to book ${classInstance.name}. Please try again.'),
            backgroundColor: Theme.of(context).colorScheme.error,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    } catch (e) {
      // Hide loading snackbar
      ScaffoldMessenger.of(context).hideCurrentSnackBar();
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error booking class: $e'),
          backgroundColor: Theme.of(context).colorScheme.error,
          duration: const Duration(seconds: 4),
        ),
      );
    } finally {
      // Clear loading state
      setState(() {
        _loadingBookings.remove('${classInstance.id}_booking');
      });
    }
  }

  void _cancelClass(ClassInstance classInstance) async {
    Navigator.pop(context);
    
    final booking = _getBookingForClass(classInstance);
    if (booking == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Booking not found for ${classInstance.name}'),
          backgroundColor: Theme.of(context).colorScheme.error,
          duration: const Duration(seconds: 3),
        ),
      );
      return;
    }

    // Set loading state for this class
    setState(() {
      _loadingBookings.add('${classInstance.id}_canceling');
    });

    // Show loading snackbar
    final loadingSnackBar = SnackBar(
      content: Row(
        children: [
          const SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          ),
          const SizedBox(width: 16),
          Text('Canceling ${classInstance.name}...'),
        ],
      ),
      backgroundColor: Theme.of(context).colorScheme.error,
      duration: const Duration(seconds: 10), // Longer duration to cover API call
    );
    
    ScaffoldMessenger.of(context).showSnackBar(loadingSnackBar);

    try {
      final success = await _classService.cancelBooking(booking['id']);
      
      // Hide loading snackbar
      ScaffoldMessenger.of(context).hideCurrentSnackBar();
      
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Successfully canceled ${classInstance.name}!'),
            backgroundColor: Colors.orange,
            duration: const Duration(seconds: 3),
          ),
        );
        // Refresh the data to show updated availability
        _loadData();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to cancel ${classInstance.name}. Please try again.'),
            backgroundColor: Theme.of(context).colorScheme.error,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    } catch (e) {
      // Hide loading snackbar
      ScaffoldMessenger.of(context).hideCurrentSnackBar();
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error canceling class: $e'),
          backgroundColor: Theme.of(context).colorScheme.error,
          duration: const Duration(seconds: 4),
        ),
      );
    } finally {
      // Clear loading state
      setState(() {
        _loadingBookings.remove('${classInstance.id}_canceling');
      });
    }
  }

  Color _getTypeColor(String type) {
    switch (type.toLowerCase()) {
      case 'karate':
        return Colors.orange;
      case 'bjj':
      case 'brazilian jiu-jitsu':
        return Colors.blue;
      case 'muay thai':
        return Colors.red;
      case 'boxing':
        return Colors.purple;
      case 'kickboxing':
        return Colors.green;
      case 'taekwondo':
        return Colors.teal;
      default:
        return Colors.grey;
    }
  }
} 