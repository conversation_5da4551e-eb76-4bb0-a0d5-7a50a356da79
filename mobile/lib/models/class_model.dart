class WeeklyClass {
  final String id;
  final String name;
  final String type;
  final int dayOfWeek;
  final String startTime;
  final String endTime;
  final String coach;
  final int maxSize;
  final int currentSize;
  final DateTime startDate;
  final bool isActive;
  final String gymId;
  final DateTime createdAt;
  final DateTime updatedAt;

  WeeklyClass({
    required this.id,
    required this.name,
    required this.type,
    required this.dayOfWeek,
    required this.startTime,
    required this.endTime,
    required this.coach,
    required this.maxSize,
    required this.currentSize,
    required this.startDate,
    required this.isActive,
    required this.gymId,
    required this.createdAt,
    required this.updatedAt,
  });

  factory WeeklyClass.fromJson(Map<String, dynamic> json) {
    return WeeklyClass(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      type: json['type'] ?? '',
      dayOfWeek: json['dayOfWeek'] ?? 0,
      startTime: json['startTime'] ?? '',
      endTime: json['endTime'] ?? '',
      coach: json['coach'] ?? '',
      maxSize: json['maxSize'] ?? 0,
      currentSize: json['currentSize'] ?? 0,
      startDate: DateTime.parse(json['startDate'] ?? DateTime.now().toIso8601String()),
      isActive: json['isActive'] ?? false,
      gymId: json['gymId'] ?? '',
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updatedAt'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'type': type,
      'dayOfWeek': dayOfWeek,
      'startTime': startTime,
      'endTime': endTime,
      'coach': coach,
      'maxSize': maxSize,
      'currentSize': currentSize,
      'startDate': startDate.toIso8601String(),
      'isActive': isActive,
      'gymId': gymId,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }
}

class ClassInstance {
  final String id;
  final String templateId;
  final String gymId;
  final String name;
  final String type;
  final String date;
  final DateTime startTime;
  final DateTime endTime;
  final String coach;
  final int maxCapacity;
  final int enrolledCount;
  final int waitlistCount;
  final String status;
  final DateTime createdAt;
  final DateTime updatedAt;

  ClassInstance({
    required this.id,
    required this.templateId,
    required this.gymId,
    required this.name,
    required this.type,
    required this.date,
    required this.startTime,
    required this.endTime,
    required this.coach,
    required this.maxCapacity,
    required this.enrolledCount,
    required this.waitlistCount,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ClassInstance.fromJson(Map<String, dynamic> json) {
    return ClassInstance(
      id: json['id'] ?? '',
      templateId: json['templateId'] ?? '',
      gymId: json['gymId'] ?? '',
      name: json['name'] ?? '',
      type: json['type'] ?? '',
      date: json['date'] ?? '',
      startTime: _parseFirestoreTimestamp(json['startTime']),
      endTime: _parseFirestoreTimestamp(json['endTime']),
      coach: json['coach'] ?? '',
      maxCapacity: json['maxCapacity'] ?? 0,
      enrolledCount: json['enrolledCount'] ?? 0,
      waitlistCount: json['waitlistCount'] ?? 0,
      status: json['status'] ?? 'scheduled',
      createdAt: _parseFirestoreTimestamp(json['createdAt']),
      updatedAt: _parseFirestoreTimestamp(json['updatedAt']),
    );
  }

  // Helper method to parse Firestore timestamp format
  static DateTime _parseFirestoreTimestamp(dynamic timestamp) {
    if (timestamp == null) {
      return DateTime.now();
    }
    
    // If it's already a DateTime, return it
    if (timestamp is DateTime) {
      return timestamp;
    }
    
    // If it's a string, try to parse as ISO8601
    if (timestamp is String) {
      try {
        return DateTime.parse(timestamp);
      } catch (e) {
        return DateTime.now();
      }
    }
    
    // If it's a Firestore timestamp object with seconds and nanos
    if (timestamp is Map<String, dynamic>) {
      final seconds = timestamp['seconds'] as int?;
      final nanos = timestamp['nanos'] as int? ?? 0;
      
      if (seconds != null) {
        return DateTime.fromMillisecondsSinceEpoch(seconds * 1000 + (nanos ~/ 1000000));
      }
    }
    
    return DateTime.now();
  }

  Map<String, dynamic> toMap() {
    return {
      'templateId': templateId,
      'gymId': gymId,
      'name': name,
      'type': type,
      'date': date,
      'startTime': startTime.toIso8601String(),
      'endTime': endTime.toIso8601String(),
      'coach': coach,
      'maxCapacity': maxCapacity,
      'enrolledCount': enrolledCount,
      'waitlistCount': waitlistCount,
      'status': status,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }
} 