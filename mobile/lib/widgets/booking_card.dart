import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:qr_flutter/qr_flutter.dart';

class BookingCard extends StatelessWidget {
  final Map<String, dynamic> classInstance;
  final bool isPast;

  const BookingCard({
    super.key,
    required this.classInstance,
    required this.isPast,
  });

  void _showQRCodeModal(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Check-in QR Code',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                classInstance['name'],
                style: Theme.of(context).textTheme.titleMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                '${DateFormat('EEEE, MMM dd').format(DateTime.parse(classInstance['date']))} at ${DateFormat('HH:mm').format(_parseTimeString(classInstance['startTime'])!)}',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
                  ),
                ),
                child: QrImageView(
                  data: classInstance['id'],
                  version: QrVersions.auto,
                  size: 280,
                  backgroundColor: Colors.white,
                ),
              ),
              const SizedBox(height: 24),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('Close'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final startTime = _parseTimeString(classInstance['startTime']);
    final endTime = _parseTimeString(classInstance['endTime']);
    
    if (startTime == null || endTime == null) {
      return const SizedBox.shrink(); // Skip invalid bookings
    }

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 2,
      color: isPast ? theme.colorScheme.surfaceVariant.withOpacity(0.5) : null,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: isPast 
            ? BorderSide(color: theme.colorScheme.outline.withOpacity(0.3), width: 1)
            : BorderSide.none,
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    classInstance['name'],
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: _getTypeColor(classInstance['type']),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        classInstance['type'],
                        style: theme.textTheme.labelSmall?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(
                  Icons.access_time,
                  size: 16,
                  color: theme.colorScheme.onSurface.withOpacity(0.6),
                ),
                const SizedBox(width: 4),
                Text(
                  '${DateFormat('HH:mm').format(startTime)} - ${DateFormat('HH:mm').format(endTime)}',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface.withOpacity(0.8),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(
                  Icons.person,
                  size: 16,
                  color: theme.colorScheme.onSurface.withOpacity(0.6),
                ),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    classInstance['coach'],
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurface.withOpacity(0.8),
                    ),
                  ),
                ),
                if (!isPast) ...[
                  IconButton(
                    onPressed: () => _showQRCodeModal(context),
                    icon: Icon(
                      Icons.qr_code,
                      color: theme.colorScheme.primary,
                      size: 24,
                    ),
                    tooltip: 'Show Check-in QR Code',
                    style: IconButton.styleFrom(
                      backgroundColor: theme.colorScheme.primaryContainer.withOpacity(0.3),
                      padding: const EdgeInsets.all(8),
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _getTypeColor(String type) {
    switch (type.toLowerCase()) {
      case 'karate':
        return Colors.orange;
      case 'bjj':
      case 'brazilian jiu-jitsu':
        return Colors.blue;
      case 'muay thai':
        return Colors.red;
      case 'boxing':
        return Colors.purple;
      case 'kickboxing':
        return Colors.green;
      case 'taekwondo':
        return Colors.teal;
      default:
        return Colors.grey;
    }
  }



  DateTime? _parseTimeString(dynamic timeValue) {
    if (timeValue is DateTime) {
      return timeValue;
    }
    if (timeValue is String) {
      try {
        // Try parsing as ISO8601 string first
        return DateTime.parse(timeValue);
      } catch (e) {
        try {
          // Try parsing as time string (HH:mm)
          final parts = timeValue.split(':');
          if (parts.length == 2) {
            final hour = int.parse(parts[0]);
            final minute = int.parse(parts[1]);
            return DateTime(2024, 1, 1, hour, minute); // Use arbitrary date
          }
        } catch (e) {
          // Ignore parsing errors
        }
      }
    }
    return null;
  }
} 