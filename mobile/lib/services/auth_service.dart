import 'dart:convert';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

class AuthService extends ChangeNotifier {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  
  // Use 127.0.0.1 for iOS simulator and local testing
  // Use 10.0.2.2 for Android emulator
  final String _baseUrl = 'http://127.0.0.1:8080/api';
  
  // Stream to track authentication state changes
  Stream<User?> get authStateChanges => _auth.authStateChanges();
  
  // Get current user
  User? get currentUser => _auth.currentUser;
  
  // User data from backend
  Map<String, dynamic>? _userData;
  Map<String, dynamic>? get userData => _userData;
  
  // Sign in with email and password
  Future<UserCredential> signInWithEmailPassword(String email, String password) async {
    try {
      print('Attempting to sign in with email: $email');
      
      // Sign in with Firebase
      final credential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      
      print('Firebase authentication successful, verifying with backend...');
      
      // Verify with backend and get user data
      await _verifyToken();
      
      return credential;
    } on FirebaseAuthException catch (e) {
      print('Firebase Auth Error: ${e.code} - ${e.message}');
      if (e.code == 'user-not-found') {
        throw Exception('No user found with this email');
      } else if (e.code == 'wrong-password') {
        throw Exception('Wrong password');
      } else if (e.code == 'invalid-credential') {
        throw Exception('Invalid email or password');
      } else {
        throw Exception('Login failed: ${e.message}');
      }
    } catch (e) {
      print('General Error: $e');
      throw Exception('Login failed: $e');
    }
  }
  
  // Verify token with backend
  Future<void> _verifyToken() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('No authenticated user');
      }
      
      print('Getting ID token for user: ${user.uid}');
      final idToken = await user.getIdToken();
      
      // The correct endpoint based on backend/internal/handlers/auth.go
      print('Sending token to backend for verification...');
      final response = await http.post(
        Uri.parse('$_baseUrl/auth/verify'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({'idToken': idToken}),
      );
      
      print('Backend response status: ${response.statusCode}');
      print('Backend response body: ${response.body}');
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        _userData = data['user'];
        
        // Check if user is a member
        if (_userData?['role'] != 'member') {
          print('User is not a member, role: ${_userData?['role']}');
          await _auth.signOut();
          throw Exception('Only members can use this app');
        }
        
        print('User verified as member: ${_userData}');
        notifyListeners();
      } else {
        String errorMessage;
        try {
          final error = jsonDecode(response.body);
          errorMessage = error['error'] ?? 'Failed to verify token';
        } catch (e) {
          errorMessage = 'Failed to verify token: ${response.body}';
        }
        throw Exception(errorMessage);
      }
    } catch (e) {
      print('Token verification error: $e');
      await _auth.signOut();
      rethrow;
    }
  }
  
  // Sign out
  Future<void> signOut() async {
    await _auth.signOut();
    _userData = null;
    notifyListeners();
  }

  // Test connection to backend
  Future<bool> testConnection() async {
    try {
      print('Testing connection to backend at $_baseUrl...');
      final response = await http.get(
        Uri.parse('$_baseUrl/health'),
        headers: {'Content-Type': 'application/json'},
      ).timeout(const Duration(seconds: 5));
      
      print('Health check response: ${response.statusCode} - ${response.body}');
      return response.statusCode == 200;
    } catch (e) {
      print('Connection test failed: $e');
      return false;
    }
  }
}
