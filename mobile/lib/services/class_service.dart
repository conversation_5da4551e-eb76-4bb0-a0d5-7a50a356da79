import 'dart:async';
import 'package:mobile/models/class_model.dart';
import 'package:mobile/services/api_service.dart';
import 'package:mobile/services/auth_service.dart';

class ClassService {
  final ApiService _apiService;
  final AuthService _authService;

  ClassService(this._authService) : _apiService = ApiService(_authService);

  // Fetch all weekly classes for a gym
  Future<List<WeeklyClass>> getWeeklyClasses() async {
    try {
      final response = await _apiService.get('/classes');
      final data = _apiService.parseResponse(response);
      final classesList = data['classes'] as List;
      return classesList.map((json) => WeeklyClass.fromJson(json)).toList();
    } catch (e) {
      print('Error fetching weekly classes: $e');
      return [];
    }
  }

  // Fetch class instances for a date range (defaults to current week)
  Future<List<ClassInstance>> getClassInstances({DateTime? startDate, DateTime? endDate}) async {
    try {
      // Default to current week (Monday to Sunday)
      final now = DateTime.now();
      final monday = now.subtract(Duration(days: now.weekday - 1));
      final sunday = monday.add(const Duration(days: 6));
      final start = startDate ?? monday;
      final end = endDate ?? sunday;
      final startStr = _formatDate(start);
      final endStr = _formatDate(end);
      
      final response = await _apiService.get('/classes/instances?startDate=$startStr&endDate=$endStr');
      final data = _apiService.parseResponse(response);
      
      if (data is Map && data['instances'] is List) {
        final instancesList = data['instances'] as List;
        return instancesList.map((json) => ClassInstance.fromJson(json)).toList();
      }
      return [];
    } catch (e) {
      print('Error fetching class instances: $e');
      return [];
    }
  }

  // Fetch all class instances (for My Bookings screen)
  Future<List<Map<String, dynamic>>> getAllClassInstances() async {
    try {
      final response = await _apiService.get('/classes/instances');
      final data = _apiService.parseResponse(response);
      
      if (data is Map && data['instances'] is List) {
        final instancesList = data['instances'] as List;
        return instancesList.cast<Map<String, dynamic>>();
      }
      return [];
    } catch (e) {
      print('Error fetching all class instances: $e');
      return [];
    }
  }

  String _formatDate(DateTime date) {
    return '${date.year.toString().padLeft(4, '0')}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  // Fetch classes for a specific day of the week
  Future<List<WeeklyClass>> getClassesByDay(int dayOfWeek) async {
    try {
      final allClasses = await getWeeklyClasses();
      return allClasses
          .where((class_) => class_.dayOfWeek == dayOfWeek && class_.isActive)
          .toList()
        ..sort((a, b) => a.startTime.compareTo(b.startTime));
    } catch (e) {
      print('Error fetching classes by day: $e');
      return [];
    }
  }

  // Fetch classes by type (martial art)
  Future<List<WeeklyClass>> getClassesByType(String type) async {
    try {
      final allClasses = await getWeeklyClasses();
      return allClasses
          .where((class_) => class_.type.toLowerCase() == type.toLowerCase() && class_.isActive)
          .toList()
        ..sort((a, b) {
          if (a.dayOfWeek != b.dayOfWeek) {
            return a.dayOfWeek.compareTo(b.dayOfWeek);
          }
          return a.startTime.compareTo(b.startTime);
        });
    } catch (e) {
      print('Error fetching classes by type: $e');
      return [];
    }
  }

  // Get unique class types for filtering
  Future<List<String>> getClassTypes() async {
    try {
      final allInstances = await getClassInstances();
      final types = allInstances
          .where((instance) => instance.status == 'scheduled')
          .map((instance) => instance.type)
          .toSet()
          .toList();
      types.sort();
      return types;
    } catch (e) {
      print('Error fetching class types: $e');
      return [];
    }
  }

  // Book a class
  Future<bool> bookClass(String classId) async {
    try {
      final response = await _apiService.post('/bookings', {
        'classId': classId,
      });
      return response.statusCode == 201;
    } catch (e) {
      print('Error booking class: $e');
      return false;
    }
  }

  // Get user bookings
  Future<List<Map<String, dynamic>>> getUserBookings() async {
    try {
      final response = await _apiService.get('/bookings');
      final data = _apiService.parseResponse(response);
      
      // The bookings endpoint returns a direct array
      if (data is List) {
        return data.cast<Map<String, dynamic>>();
      }
      
      // If data is null (empty response), return empty array
      if (data == null) {
        return [];
      }
      
      return [];
    } catch (e) {
      print('Error fetching user bookings: $e');
      return [];
    }
  }

  // Cancel a booking
  Future<bool> cancelBooking(String bookingId) async {
    try {
      final response = await _apiService.delete('/bookings/$bookingId');
      return response.statusCode == 200;
    } catch (e) {
      print('Error canceling booking: $e');
      return false;
    }
  }

  // Get day name from day of week number
  String getDayName(int dayOfWeek) {
    const days = [
      'Sunday',
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday'
    ];
    return days[dayOfWeek];
  }

  // Get short day name
  String getShortDayName(int dayOfWeek) {
    const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    return days[dayOfWeek];
  }
} 