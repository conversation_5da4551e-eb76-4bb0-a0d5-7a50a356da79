import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:mobile/services/auth_service.dart';

import 'dart:io';

class ApiService {
  // Use 10.0.2.2 for Android emulator, 127.0.0.1 for iOS simulator and local testing
  static String get baseUrl {
    if (Platform.isAndroid) {
      return 'http://10.0.2.2:8080/api/v1';
    } else {
      return 'http://127.0.0.1:8080/api/v1';
    }
  }
  
  final AuthService _authService;
  
  ApiService(this._authService);

  // Get the current user's ID token for authentication
  Future<String?> _getAuthToken() async {
    try {
      final user = _authService.currentUser;
      if (user != null) {
        return await user.getIdToken();
      }
    } catch (e) {
      print('Error getting auth token: $e');
    }
    return null;
  }

  // Make an authenticated GET request
  Future<http.Response> get(String endpoint) async {
    final token = await _getAuthToken();
    final headers = {
      'Content-Type': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    };

    final response = await http.get(
      Uri.parse('$baseUrl$endpoint'),
      headers: headers,
    );

    _handleResponse(response);
    return response;
  }

  // Make an authenticated POST request
  Future<http.Response> post(String endpoint, Map<String, dynamic> data) async {
    final token = await _getAuthToken();
    final headers = {
      'Content-Type': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    };

    final response = await http.post(
      Uri.parse('$baseUrl$endpoint'),
      headers: headers,
      body: jsonEncode(data),
    );

    _handleResponse(response);
    return response;
  }

  // Make an authenticated PUT request
  Future<http.Response> put(String endpoint, Map<String, dynamic> data) async {
    final token = await _getAuthToken();
    final headers = {
      'Content-Type': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    };

    final response = await http.put(
      Uri.parse('$baseUrl$endpoint'),
      headers: headers,
      body: jsonEncode(data),
    );

    _handleResponse(response);
    return response;
  }

  // Make an authenticated DELETE request
  Future<http.Response> delete(String endpoint) async {
    final token = await _getAuthToken();
    final headers = {
      'Content-Type': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    };

    final response = await http.delete(
      Uri.parse('$baseUrl$endpoint'),
      headers: headers,
    );

    _handleResponse(response);
    return response;
  }

  // Handle common response errors
  void _handleResponse(http.Response response) {
    if (response.statusCode >= 400) {
      String errorMessage = 'Request failed';
      try {
        final errorData = jsonDecode(response.body);
        errorMessage = errorData['error'] ?? errorMessage;
      } catch (e) {
        errorMessage = response.reasonPhrase ?? errorMessage;
      }
      throw ApiException(errorMessage, response.statusCode);
    }
  }

  // Parse JSON response
  dynamic parseResponse(http.Response response) {
    try {
      // Handle empty response body
      if (response.body.isEmpty) {
        print('Empty response body for ${response.request?.url}');
        return null;
      }
      return jsonDecode(response.body);
    } catch (e) {
      throw ApiException('Failed to parse response: $e', response.statusCode);
    }
  }
}

// Custom exception for API errors
class ApiException implements Exception {
  final String message;
  final int statusCode;

  ApiException(this.message, this.statusCode);

  @override
  String toString() => 'ApiException: $message (Status: $statusCode)';
} 