import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBFftyFgdFIKEfYtAEPsHN6JnQKJj-fCB4',
    appId: '1:97926210852:web:3f4c35ca8e034b5c317fb1',
    messagingSenderId: '97926210852',
    projectId: 'dojofy-7c306',
    authDomain: 'dojofy-7c306.firebaseapp.com',
    storageBucket: 'dojofy-7c306.appspot.com',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAFBmcQUk6H1ynH7aLXm_healUJyj4gZxI',
    appId: '1:97926210852:android:b0552fba93be985b317fb1',
    messagingSenderId: '97926210852',
    projectId: 'dojofy-7c306',
    storageBucket: 'dojofy-7c306.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDa-YwgWTp2GDyVYEfv-XLb62100_HoEvU',
    appId: '1:97926210852:ios:c567e2210a8f0bb8317fb1',
    messagingSenderId: '97926210852',
    projectId: 'dojofy-7c306',
    storageBucket: 'dojofy-7c306.appspot.com',
    iosClientId: '97926210852-0vjo21cjh5lbm2p8o2vfa5k5c5c7ghqd.apps.googleusercontent.com',
    iosBundleId: 'com.example.mobile',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDa-YwgWTp2GDyVYEfv-XLb62100_HoEvU',
    appId: '1:97926210852:ios:c567e2210a8f0bb8317fb1',
    messagingSenderId: '97926210852',
    projectId: 'dojofy-7c306',
    storageBucket: 'dojofy-7c306.appspot.com',
    iosClientId: '97926210852-0vjo21cjh5lbm2p8o2vfa5k5c5c7ghqd.apps.googleusercontent.com',
    iosBundleId: 'com.example.mobile',
  );
}