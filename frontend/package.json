{"name": "martial-arts-gym-frontend", "version": "0.0.0", "private": true, "scripts": {"build": "vite build", "dev": "vite", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@headlessui/vue": "^1.7.16", "@heroicons/vue": "^2.0.18", "@vueuse/firebase": "^10.5.0", "chart.js": "^4.4.0", "firebase": "^10.5.2", "pinia": "^2.1.7", "vue": "^3.3.8", "vue-chartjs": "^5.2.0", "vue-router": "^4.2.5"}, "devDependencies": {"@rushstack/eslint-patch": "^1.3.3", "@tailwindcss/forms": "^0.5.10", "@vitejs/plugin-vue": "^4.4.1", "@vue/eslint-config-prettier": "^8.0.0", "autoprefixer": "^10.4.21", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "postcss": "^8.5.5", "prettier": "^3.0.3", "tailwindcss": "^3.4.17", "vite": "^4.4.11"}}