@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Custom styles */
body {
  font-family: 'Inter', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
}

/* Loading spinner */
.spinner {
  @apply animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600;
}

/* Button styles */
.btn-primary {
  @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-secondary {
  @apply bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-danger {
  @apply bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed;
}

/* Card styles */
.card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
}

/* Form input styles */
.form-input {
  @apply block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

/* Alert styles */
.alert-success {
  @apply bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-md;
}

.alert-error {
  @apply bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-md;
}

.alert-info {
  @apply bg-blue-50 border border-blue-200 text-blue-800 px-4 py-3 rounded-md;
}

/* Navigation styles */
.nav-link {
  @apply flex items-center px-4 py-2 text-sm font-medium text-gray-700 hover:text-primary-600 hover:bg-gray-50 rounded-md transition-colors duration-200;
}

.nav-link.active {
  @apply text-primary-600 bg-primary-50;
}

/* Table styles */
.table {
  @apply min-w-full divide-y divide-gray-200;
}

.table th {
  @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50;
}

.table td {
  @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
}

.table tbody tr {
  @apply border-b border-gray-200 hover:bg-gray-50;
}

/* Responsive utilities */
@responsive {
  .container-padding {
    @apply px-4 sm:px-6 lg:px-8;
  }
}

/* Smooth transitions */
* {
  @apply transition-colors duration-200;
}

/* Focus styles for accessibility */
button:focus,
input:focus,
select:focus,
textarea:focus {
  @apply outline-none ring-2 ring-primary-500 ring-offset-2;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-300 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400;
}