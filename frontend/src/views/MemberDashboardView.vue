<template>
  <div class="member-dashboard">
    <h1>Member Dashboard</h1>
    <p>Welcome to your member dashboard!</p>
    
    <div class="member-info" v-if="authStore.user">
      <h2>Your Information</h2>
      <p><strong>Name:</strong> {{ authStore.userName }}</p>
      <p><strong>Email:</strong> {{ authStore.userEmail }}</p>
    </div>
    
    <div class="actions">
      <button @click="authStore.signOut" class="btn btn-danger">
        Sign Out
      </button>
    </div>
  </div>
</template>

<script setup>
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()
</script>

<style scoped>
.member-dashboard {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
}

.member-info {
  margin-top: 2rem;
  padding: 1rem;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.actions {
  margin-top: 2rem;
}
</style>