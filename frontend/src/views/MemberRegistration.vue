<template>
  <div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
      <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
        Create your member account
      </h2>
    </div>

    <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
      <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
        <div v-if="loading" class="flex justify-center">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
        
        <div v-else-if="error" class="rounded-md bg-red-50 p-4 mb-4">
          <div class="flex">
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800">Registration Error</h3>
              <div class="mt-2 text-sm text-red-700">
                <p>{{ error }}</p>
              </div>
            </div>
          </div>
        </div>
        
        <div v-else-if="success" class="rounded-md bg-green-50 p-4 mb-4">
          <div class="flex">
            <div class="ml-3">
              <h3 class="text-sm font-medium text-green-800">Registration Successful</h3>
              <div class="mt-2 text-sm text-green-700">
                <p>Your account has been created successfully. You can now log in.</p>
              </div>
              <div class="mt-4">
                <router-link 
                  to="/login" 
                  class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Go to Login
                </router-link>
              </div>
            </div>
          </div>
        </div>
        
        <form v-else class="space-y-6" @submit.prevent="register">
          <div>
            <label for="password" class="block text-sm font-medium text-gray-700">
              Password
            </label>
            <div class="mt-1">
              <input 
                id="password" 
                v-model="password"
                name="password" 
                type="password" 
                required 
                class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
            </div>
          </div>

          <div>
            <label for="confirmPassword" class="block text-sm font-medium text-gray-700">
              Confirm Password
            </label>
            <div class="mt-1">
              <input 
                id="confirmPassword" 
                v-model="confirmPassword"
                name="confirmPassword" 
                type="password" 
                required 
                class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
            </div>
          </div>

          <div>
            <button 
              type="submit" 
              class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              :disabled="loading"
            >
              Create Account
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// Form data
const password = ref('')
const confirmPassword = ref('')

// Component state
const loading = ref(false)
const error = ref('')
const success = ref(false)
const memberData = ref(null)

onMounted(async () => {
  // Get query parameters
  const memberId = route.query.memberId
  const accessCode = route.query.accessCode
  
  if (!memberId || !accessCode) {
    error.value = 'Invalid invitation link. Please check your email for the correct link.'
    return
  }
  
  loading.value = true
  try {
    // Verify the invitation
    const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/v1/auth/verify-invitation`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ memberId, accessCode }),
    })
    
    if (!response.ok) {
      const data = await response.json()
      throw new Error(data.error || 'Invalid or expired invitation')
    }
    
    const data = await response.json()
    memberData.value = data.member
  } catch (err) {
    error.value = err.message || 'Failed to verify invitation'
  } finally {
    loading.value = false
  }
})

const register = async () => {
  // Validate passwords
  if (password.value !== confirmPassword.value) {
    error.value = 'Passwords do not match'
    return
  }
  
  if (password.value.length < 6) {
    error.value = 'Password must be at least 6 characters'
    return
  }
  
  loading.value = true
  error.value = ''
  
  try {
    // Register the member
    await authStore.registerMember(
      memberData.value.email,
      password.value,
      route.query.memberId,
      route.query.accessCode
    )
    
    success.value = true
  } catch (err) {
    error.value = err.message || 'Registration failed'
  } finally {
    loading.value = false
  }
}
</script>
