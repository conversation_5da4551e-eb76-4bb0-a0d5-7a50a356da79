<template>
  <LayoutComponent>
    <!-- Header -->
    <div class="sm:flex sm:items-center sm:justify-between mb-6">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Membership Plans</h1>
        <p class="mt-2 text-sm text-gray-700">
          Manage your gym's membership plans and pricing.
        </p>
      </div>
      <div class="mt-4 sm:mt-0">
        <button
          @click="openCreateModal"
          class="btn-primary inline-flex items-center"
        >
          <PlusIcon class="mr-2 h-4 w-4" />
          Add Plan
        </button>
      </div>
    </div>

    <!-- Error <PERSON>ert -->
    <div v-if="membershipsStore.error" class="mb-4 bg-red-50 p-4 rounded-md">
      <div class="flex">
        <div class="flex-shrink-0">
          <ExclamationTriangleIcon class="h-5 w-5 text-red-400" />
        </div>
        <div class="ml-3">
          <p class="text-sm text-red-700">{{ membershipsStore.error }}</p>
        </div>
        <div class="ml-auto pl-3">
          <button @click="membershipsStore.clearError" class="text-red-400 hover:text-red-600">
            <XMarkIcon class="h-5 w-5" />
          </button>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="membershipsStore.loading" class="flex justify-center py-8">
      <div class="spinner"></div>
    </div>

    <!-- Empty State -->
    <div v-else-if="membershipsStore.plans.length === 0" class="text-center py-8">
      <CreditCardIcon class="mx-auto h-12 w-12 text-gray-400 mb-4" />
      <h3 class="text-lg font-medium text-gray-900 mb-2">No membership plans</h3>
      <p class="text-gray-500 mb-4">
        Get started by creating your first membership plan.
      </p>
      <button @click="openCreateModal" class="btn-primary">
        Add Membership Plan
      </button>
    </div>

    <!-- Plans Grid -->
    <div v-else class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
      <div 
        v-for="plan in membershipsStore.plans" 
        :key="plan.id"
        :class="[
          'overflow-hidden rounded-lg border',
          plan.isActive ? 'border-gray-200' : 'border-gray-200 bg-gray-50'
        ]"
      >
        <div class="px-4 py-5 sm:p-6">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium leading-6 text-gray-900">{{ plan.name }}</h3>
            <span 
              :class="[
                'inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium',
                plan.isActive 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-gray-100 text-gray-800'
              ]"
            >
              {{ plan.isActive ? 'Active' : 'Inactive' }}
            </span>
          </div>
          
          <div class="mt-2">
            <p class="text-sm text-gray-500">{{ plan.description || 'No description provided' }}</p>
          </div>
          
          <div class="mt-4">
            <div class="flex items-baseline">
              <span class="text-2xl font-bold text-gray-900">{{ formatCurrency(plan.price, plan.currency) }}</span>
              <span class="ml-1 text-sm text-gray-500">/ {{ formatBillingCycle(plan.billingCycle) }}</span>
            </div>
          </div>
          
          <div class="mt-4 space-y-2">
            <div class="flex items-center">
              <CalendarIcon class="h-5 w-5 text-gray-400 mr-2" />
              <span class="text-sm text-gray-700">
                {{ plan.classesPerWeek === 0 ? 'Unlimited classes' : `${plan.classesPerWeek} classes per week` }}
              </span>
            </div>
            <div class="flex items-center">
              <UserIcon class="h-5 w-5 text-gray-400 mr-2" />
              <span class="text-sm text-gray-700">
                {{ plan.includesPrivate ? 'Includes private lessons' : 'No private lessons' }}
              </span>
            </div>
          </div>
          
          <div class="mt-5 flex space-x-2">
            <button 
              @click="openEditModal(plan)" 
              class="btn-secondary text-sm flex-1"
            >
              Edit
            </button>
            <button 
              @click="openActionsModal(plan)" 
              class="btn-secondary text-sm"
            >
              <EllipsisVerticalIcon class="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Membership Form Modal -->
    <MembershipFormModal
      :open="isModalOpen"
      :plan="selectedPlan"
      @close="closeModal"
      @save="handleSavePlan"
    />

    <!-- Delete Confirmation Modal -->
    <ConfirmationModal
      :open="isDeleteModalOpen"
      title="Delete Membership Plan"
      :message="`Are you sure you want to delete the '${planToDelete?.name}' plan? This action cannot be undone.`"
      confirm-text="Delete"
      @close="isDeleteModalOpen = false"
      @confirm="handleDeletePlan"
    />

    <!-- Plan Actions Modal -->
    <div v-if="showActionsModal" class="fixed inset-0 z-50 overflow-y-auto">
      <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <!-- Background overlay -->
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="showActionsModal = false"></div>

        <!-- Modal panel -->
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Plan Actions</h3>
            
            <div class="space-y-2">
              <button
                @click="togglePlanStatus(selectedActionPlan); showActionsModal = false"
                class="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md"
              >
                <component 
                  :is="selectedActionPlan?.isActive ? StopIcon : PlayIcon" 
                  class="mr-3 h-5 w-5" 
                />
                {{ selectedActionPlan?.isActive ? 'Deactivate Plan' : 'Activate Plan' }}
              </button>
              
              <button
                @click="confirmDelete(selectedActionPlan); showActionsModal = false"
                class="flex w-full items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md"
              >
                <TrashIcon class="mr-3 h-5 w-5" />
                Delete Plan
              </button>
            </div>
          </div>
          
          <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button 
              type="button" 
              class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
              @click="showActionsModal = false"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  </LayoutComponent>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useMembershipsStore } from '@/stores/memberships'
import LayoutComponent from '@/components/LayoutComponent.vue'
import MembershipFormModal from '@/components/MembershipFormModal.vue'
import ConfirmationModal from '@/components/ConfirmationModal.vue'
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/vue'
import {
  PlusIcon,
  ExclamationTriangleIcon,
  XMarkIcon,
  CreditCardIcon,
  CalendarIcon,
  UserIcon,
  EllipsisVerticalIcon,
  TrashIcon,
  StopIcon,
  PlayIcon,
} from '@heroicons/vue/24/outline'

const membershipsStore = useMembershipsStore()

// Component state
const isModalOpen = ref(false)
const selectedPlan = ref(null)
const isDeleteModalOpen = ref(false)
const planToDelete = ref(null)
const showActionsModal = ref(false)
const selectedActionPlan = ref(null)

// Load plans on component mount
onMounted(async () => {
  try {
    await membershipsStore.fetchMembershipPlans()
  } catch (error) {
    console.error('Failed to load membership plans:', error)
  }
})

// Modal methods
const openCreateModal = () => {
  selectedPlan.value = null
  isModalOpen.value = true
}

const openEditModal = (plan) => {
  selectedPlan.value = plan
  isModalOpen.value = true
}

const closeModal = () => {
  isModalOpen.value = false
  selectedPlan.value = null
}

// Plan actions
const handleSavePlan = async (planData) => {
  try {
    if (selectedPlan.value) {
      // Update existing plan
      await membershipsStore.updateMembershipPlan(selectedPlan.value.id, planData)
    } else {
      // Create new plan
      await membershipsStore.createMembershipPlan(planData)
    }
    closeModal()
  } catch (error) {
    console.error('Failed to save membership plan:', error)
  }
}

const togglePlanStatus = async (plan) => {
  try {
    await membershipsStore.togglePlanStatus(plan)
  } catch (error) {
    console.error('Failed to update plan status:', error)
  }
}

const confirmDelete = (plan) => {
  planToDelete.value = plan
  isDeleteModalOpen.value = true
}

const handleDeletePlan = async () => {
  try {
    await membershipsStore.deleteMembershipPlan(planToDelete.value.id)
    isDeleteModalOpen.value = false
    planToDelete.value = null
  } catch (error) {
    console.error('Failed to delete membership plan:', error)
  }
}

// Utility methods
const formatCurrency = (amount, currency) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency || 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 2
  }).format(amount)
}

const formatBillingCycle = (cycle) => {
  switch (cycle) {
    case 'monthly':
      return 'month'
    case 'quarterly':
      return 'quarter'
    case 'yearly':
      return 'year'
    default:
      return cycle
  }
}

const toggleDropdown = (planId) => {
  if (activeDropdown.value === planId) {
    activeDropdown.value = null
  } else {
    activeDropdown.value = planId
  }
}

// Open actions modal
const openActionsModal = (plan) => {
  selectedActionPlan.value = plan
  showActionsModal.value = true
}
</script>
