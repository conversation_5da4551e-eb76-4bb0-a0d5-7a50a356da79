<!-- src/views/MembersView.vue -->
<template>
  <LayoutComponent>
    <!-- Header -->
    <div class="sm:flex sm:items-center sm:justify-between mb-6">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Members</h1>
        <p class="mt-2 text-sm text-gray-700">
          Manage your gym members and their information.
        </p>
      </div>
      <div class="mt-4 sm:mt-0">
        <button
          @click="openCreateModal"
          class="btn-primary inline-flex items-center"
        >
          <PlusIcon class="mr-2 h-4 w-4" />
          Add Member
        </button>
      </div>
    </div>

    <!-- Error Message -->
    <div v-if="membersStore.error" class="mb-6 alert-error">
      <div class="flex">
        <div class="flex-shrink-0">
          <ExclamationTriangleIcon class="h-5 w-5" />
        </div>
        <div class="ml-3">
          <p class="text-sm">{{ membersStore.error }}</p>
        </div>
        <div class="ml-auto pl-3">
          <button @click="membersStore.clearError" class="text-red-400 hover:text-red-600">
            <XMarkIcon class="h-5 w-5" />
          </button>
        </div>
      </div>
    </div>

    <!-- Search and Filter -->
    <div class="mb-6 flex flex-col sm:flex-row gap-4">
      <div class="flex-1">
        <label for="search" class="sr-only">Search members</label>
        <div class="relative">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <MagnifyingGlassIcon class="h-5 w-5 text-gray-400" />
          </div>
          <input
            id="search"
            v-model="searchQuery"
            type="text"
            placeholder="Search members..."
            class="form-input pl-10"
          />
        </div>
      </div>
      <div class="sm:w-48">
        <select v-model="statusFilter" class="form-input">
          <option value="">All Members</option>
          <option value="active">Active Only</option>
          <option value="inactive">Inactive Only</option>
        </select>
      </div>
    </div>

    <!-- Members Table -->
    <div class="card overflow-hidden">
      <div v-if="membersStore.loading" class="flex justify-center py-8">
        <div class="spinner"></div>
      </div>

      <div v-else-if="filteredMembers.length === 0" class="text-center py-8">
        <UsersIcon class="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <h3 class="text-lg font-medium text-gray-900 mb-2">No members found</h3>
        <p class="text-gray-500 mb-4">
          {{ searchQuery || statusFilter ? 'Try adjusting your search criteria.' : 'Get started by adding your first member.' }}
        </p>
        <button @click="openCreateModal" class="btn-primary">
          Add Member
        </button>
      </div>

      <div v-else class="overflow-x-auto">
        <table class="table">
          <thead>
            <tr>
              <th>Name</th>
              <th>Email</th>
              <th>Martial Art</th>
              <th>Payment Method</th>
              <th>Status</th>
              <th>Join Date</th>
              <th class="relative px-6 py-3">
                <span class="sr-only">Actions</span>
              </th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="member in filteredMembers" :key="member.id">
              <td>
                <div class="flex items-center">
                  <div class="h-10 w-10 flex-shrink-0">
                    <div class="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                      <span class="text-sm font-medium text-gray-700">
                        {{ member.firstName.charAt(0) }}{{ member.lastName.charAt(0) }}
                      </span>
                    </div>
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">
                      {{ member.firstName }} {{ member.lastName }}
                    </div>
                    <div v-if="member.phone" class="text-sm text-gray-500">
                      {{ member.phone }}
                    </div>
                  </div>
                </div>
              </td>
              <td>
                <div class="text-sm text-gray-900">{{ member.email }}</div>
              </td>
              <td>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                  {{ member.martialArt }}
                </span>
              </td>
              <td>
                <div class="text-sm text-gray-900">{{ member.paymentMethod }}</div>
              </td>
              <td>
                <span :class="[
                  'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                  member.isActive 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                ]">
                  {{ member.isActive ? 'Active' : 'Inactive' }}
                </span>
              </td>
              <td>
                <div class="text-sm text-gray-900">
                  {{ formatDate(member.joinDate) }}
                </div>
              </td>
              <td class="text-right text-sm font-medium">
                <Menu as="div" class="relative inline-block text-left">
                  <div>
                    <MenuButton class="flex items-center text-gray-400 hover:text-gray-600">
                      <EllipsisVerticalIcon class="h-5 w-5" />
                    </MenuButton>
                  </div>
                  <transition
                    enter-active-class="transition ease-out duration-100"
                    enter-from-class="transform opacity-0 scale-95"
                    enter-to-class="transform opacity-100 scale-100"
                    leave-active-class="transition ease-in duration-75"
                    leave-from-class="transform opacity-100 scale-100"
                    leave-to-class="transform opacity-0 scale-95"
                  >
                    <MenuItems class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-10">
                      <div class="py-1">
                        <MenuItem v-slot="{ active }">
                          <button
                            @click="openEditModal(member)"
                            :class="[
                              active ? 'bg-gray-100 text-gray-900' : 'text-gray-700',
                              'group flex items-center px-4 py-2 text-sm w-full'
                            ]"
                          >
                            <PencilIcon class="mr-3 h-4 w-4" />
                            Edit
                          </button>
                        </MenuItem>
                        <MenuItem v-slot="{ active }">
                          <button
                            @click="toggleMemberStatus(member)"
                            :class="[
                              active ? 'bg-gray-100 text-gray-900' : 'text-gray-700',
                              'group flex items-center px-4 py-2 text-sm w-full'
                            ]"
                          >
                            <component 
                              :is="member.isActive ? StopIcon : PlayIcon" 
                              class="mr-3 h-4 w-4" 
                            />
                            {{ member.isActive ? 'Deactivate' : 'Activate' }}
                          </button>
                        </MenuItem>
                        <MenuItem v-slot="{ active }">
                          <button
                            @click="confirmDelete(member)"
                            :class="[
                              active ? 'bg-gray-100 text-gray-900' : 'text-gray-700',
                              'group flex items-center px-4 py-2 text-sm w-full'
                            ]"
                          >
                            <TrashIcon class="mr-3 h-4 w-4" />
                            Delete
                          </button>
                        </MenuItem>
                        <MenuItem v-slot="{ active }">
                          <button
                            @click="sendInvite(member)"
                            :class="[
                              active ? 'bg-gray-100 text-gray-900' : 'text-gray-700',
                              'group flex items-center px-4 py-2 text-sm w-full'
                            ]"
                          >
                            <EnvelopeIcon class="mr-3 h-4 w-4" />
                            Send Invitation
                          </button>
                        </MenuItem>
                      </div>
                    </MenuItems>
                  </transition>
                </Menu>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Member Form Modal -->
    <MemberFormModal
      :open="isModalOpen"
      :member="selectedMember"
      @close="closeModal"
      @save="handleSaveMember"
    />

    <!-- Delete Confirmation Modal -->
    <ConfirmationModal
      :open="isDeleteModalOpen"
      title="Delete Member"
      :message="`Are you sure you want to delete ${memberToDelete?.firstName} ${memberToDelete?.lastName}? This action cannot be undone.`"
      confirm-text="Delete"
      @close="isDeleteModalOpen = false"
      @confirm="handleDeleteMember"
    />

    <!-- Invitation Link Modal -->
    <InvitationLinkModal
      :open="isInviteModalOpen"
      :invite-link="inviteLink"
      @close="isInviteModalOpen = false"
    />
  </LayoutComponent>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useMembersStore } from '@/stores/members'
import { useMembershipsStore } from '@/stores/memberships'
import LayoutComponent from '@/components/LayoutComponent.vue'
import MemberFormModal from '@/components/MemberFormModal.vue'
import ConfirmationModal from '@/components/ConfirmationModal.vue'
import InvitationLinkModal from '@/components/InvitationLinkModal.vue'
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/vue'
import {
  PlusIcon,
  ExclamationTriangleIcon,
  XMarkIcon,
  MagnifyingGlassIcon,
  UsersIcon,
  EllipsisVerticalIcon,
  PencilIcon,
  TrashIcon,
  StopIcon,
  PlayIcon,
  EnvelopeIcon,
} from '@heroicons/vue/24/outline'

// Stores
const membersStore = useMembersStore()
const membershipsStore = useMembershipsStore()

// Component state
const searchQuery = ref('')
const statusFilter = ref('')
const isModalOpen = ref(false)
const selectedMember = ref(null)
const isDeleteModalOpen = ref(false)
const memberToDelete = ref(null)
const isInviteModalOpen = ref(false)
const inviteLink = ref('')
const memberToInvite = ref(null)

// Load data on component mount
onMounted(async () => {
  try {
    await membersStore.fetchMembers()
    if (membershipsStore.plans.length === 0) {
      await membershipsStore.fetchMembershipPlans()
    }
  } catch (error) {
    console.error('Failed to load data:', error)
  }
})

// Computed properties
const filteredMembers = computed(() => {
  let filtered = membersStore.members

  // Filter by search query
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(member =>
      member.firstName.toLowerCase().includes(query) ||
      member.lastName.toLowerCase().includes(query) ||
      member.email.toLowerCase().includes(query) ||
      member.martialArt.toLowerCase().includes(query)
    )
  }

  // Filter by status
  if (statusFilter.value === 'active') {
    filtered = filtered.filter(member => member.isActive)
  } else if (statusFilter.value === 'inactive') {
    filtered = filtered.filter(member => !member.isActive)
  }

  return filtered
})

// Modal methods
const openCreateModal = () => {
  selectedMember.value = null
  isModalOpen.value = true
}

const openEditModal = (member) => {
  selectedMember.value = member
  isModalOpen.value = true
}

const closeModal = () => {
  isModalOpen.value = false
  selectedMember.value = null
}

// Member actions
const handleSaveMember = async (memberData) => {
  try {
    if (selectedMember.value) {
      // Update existing member
      await membersStore.updateMember(selectedMember.value.id, memberData)
    } else {
      // Create new member
      await membersStore.createMember(memberData)
    }
    closeModal()
  } catch (error) {
    console.error('Failed to save member:', error)
  }
}

const toggleMemberStatus = async (member) => {
  try {
    await membersStore.updateMember(member.id, { isActive: !member.isActive })
  } catch (error) {
    console.error('Failed to update member status:', error)
  }
}

const confirmDelete = (member) => {
  memberToDelete.value = member
  isDeleteModalOpen.value = true
}

const handleDeleteMember = async () => {
  try {
    await membersStore.deleteMember(memberToDelete.value.id)
    isDeleteModalOpen.value = false
    memberToDelete.value = null
  } catch (error) {
    console.error('Failed to delete member:', error)
  }
}

const sendInvite = async (member) => {
  try {
    memberToInvite.value = member
    const response = await membersStore.sendInvitation(member.id)
    inviteLink.value = response.inviteLink
    isInviteModalOpen.value = true
  } catch (error) {
    console.error('Failed to send invitation:', error)
  }
}

// Utility methods
const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString()
}

// Helper function to get membership plan name
const getMembershipPlanName = (planId) => {
  if (!planId) return 'No plan'
  const plan = membershipsStore.plans.find(p => p.id === planId)
  return plan ? plan.name : 'Unknown plan'
}
</script>
