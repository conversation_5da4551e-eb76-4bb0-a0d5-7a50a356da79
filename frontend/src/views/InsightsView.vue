<!-- src/views/InsightsView.vue -->
<template>
  <LayoutComponent>
    <!-- Header -->
    <div class="mb-8">
      <div class="md:flex md:items-center md:justify-between">
        <div class="flex-1 min-w-0">
          <h1 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            Insights
          </h1>
          <p class="mt-1 text-sm text-gray-500">
            Detailed analytics and insights about your gym's performance.
          </p>
        </div>
        <div class="mt-4 flex md:mt-0 md:ml-4">
          <button
            @click="refreshData"
            :disabled="loading"
            class="btn-secondary inline-flex items-center"
          >
            <ArrowPathIcon 
              :class="['mr-2 h-4 w-4', loading ? 'animate-spin' : '']" 
            />
            Refresh
          </button>
        </div>
      </div>
    </div>

    <!-- Error Message -->
    <div v-if="dashboardStore.error" class="mb-6 alert-error">
      <div class="flex">
        <div class="flex-shrink-0">
          <ExclamationTriangleIcon class="h-5 w-5" />
        </div>
        <div class="ml-3">
          <p class="text-sm">{{ dashboardStore.error }}</p>
        </div>
        <div class="ml-auto pl-3">
          <button @click="dashboardStore.clearError" class="text-red-400 hover:text-red-600">
            <XMarkIcon class="h-5 w-5" />
          </button>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="flex justify-center py-12">
      <div class="spinner"></div>
    </div>

    <div v-else class="space-y-8">
      <!-- Member Growth Stats -->
      <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
        <!-- New Members This Month -->
        <div class="card">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="p-3 bg-green-100 rounded-md">
                <UserPlusIcon class="h-6 w-6 text-green-600" />
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">
                  New Members This Month
                </dt>
                <dd class="text-2xl font-semibold text-gray-900">
                  {{ dashboardStore.memberGrowth.newMembersThisMonth }}
                </dd>
              </dl>
            </div>
          </div>
        </div>

        <!-- New Members Last Month -->
        <div class="card">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="p-3 bg-blue-100 rounded-md">
                <UsersIcon class="h-6 w-6 text-blue-600" />
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">
                  New Members Last Month
                </dt>
                <dd class="text-2xl font-semibold text-gray-900">
                  {{ dashboardStore.memberGrowth.newMembersLastMonth }}
                </dd>
              </dl>
            </div>
          </div>
        </div>

        <!-- Growth Trend -->
        <div class="card">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="p-3 bg-purple-100 rounded-md">
                <ArrowTrendingUpIcon class="h-6 w-6 text-purple-600" />
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">
                  Growth Trend
                </dt>
                <dd class="text-2xl font-semibold" :class="growthTrendColor">
                  {{ growthTrendText }}
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <!-- Member Growth Chart -->
      <div class="card">
        <div class="border-b border-gray-200 pb-4 mb-6">
          <h3 class="text-lg font-medium text-gray-900">Member Growth Over Time</h3>
          <p class="text-sm text-gray-500 mt-1">
            Track how your member base has grown over the last 6 months
          </p>
        </div>
        
        <div v-if="chartData.labels.length > 0" class="h-64">
          <Line :data="chartData" :options="chartOptions" />
        </div>
        
        <div v-else class="text-center py-8">
          <ChartBarIcon class="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h4 class="text-lg font-medium text-gray-900 mb-2">No Data Available</h4>
          <p class="text-gray-500">
            Member growth data will appear here once you have more members.
          </p>
        </div>
      </div>

      <!-- Member Statistics -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Member Status Breakdown -->
        <div class="card">
          <div class="border-b border-gray-200 pb-4 mb-6">
            <h3 class="text-lg font-medium text-gray-900">Member Status</h3>
          </div>
          
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                <span class="text-sm font-medium text-gray-900">Active Members</span>
              </div>
              <span class="text-sm text-gray-500">
                {{ dashboardStore.stats.activeMembers }} 
                ({{ memberStatusPercentage.active }}%)
              </span>
            </div>
            
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="w-3 h-3 bg-red-500 rounded-full mr-3"></div>
                <span class="text-sm font-medium text-gray-900">Inactive Members</span>
              </div>
              <span class="text-sm text-gray-500">
                {{ inactiveMembers }} 
                ({{ memberStatusPercentage.inactive }}%)
              </span>
            </div>
            
            <!-- Status Progress Bars -->
            <div class="mt-4 space-y-3">
              <div>
                <div class="flex justify-between text-sm text-gray-600 mb-1">
                  <span>Active</span>
                  <span>{{ memberStatusPercentage.active }}%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    class="bg-green-500 h-2 rounded-full transition-all duration-300"
                    :style="{ width: `${memberStatusPercentage.active}%` }"
                  ></div>
                </div>
              </div>
              
              <div>
                <div class="flex justify-between text-sm text-gray-600 mb-1">
                  <span>Inactive</span>
                  <span>{{ memberStatusPercentage.inactive }}%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    class="bg-red-500 h-2 rounded-full transition-all duration-300"
                    :style="{ width: `${memberStatusPercentage.inactive}%` }"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Class Statistics -->
        <div class="card">
          <div class="border-b border-gray-200 pb-4 mb-6">
            <h3 class="text-lg font-medium text-gray-900">Class Status</h3>
          </div>
          
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="w-3 h-3 bg-blue-500 rounded-full mr-3"></div>
                <span class="text-sm font-medium text-gray-900">Active Classes</span>
              </div>
              <span class="text-sm text-gray-500">
                {{ dashboardStore.stats.activeClasses }} 
                ({{ classStatusPercentage.active }}%)
              </span>
            </div>
            
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="w-3 h-3 bg-gray-500 rounded-full mr-3"></div>
                <span class="text-sm font-medium text-gray-900">Inactive Classes</span>
              </div>
              <span class="text-sm text-gray-500">
                {{ inactiveClasses }} 
                ({{ classStatusPercentage.inactive }}%)
              </span>
            </div>
            
            <!-- Class Progress Bars -->
            <div class="mt-4 space-y-3">
              <div>
                <div class="flex justify-between text-sm text-gray-600 mb-1">
                  <span>Active</span>
                  <span>{{ classStatusPercentage.active }}%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    class="bg-blue-500 h-2 rounded-full transition-all duration-300"
                    :style="{ width: `${classStatusPercentage.active}%` }"
                  ></div>
                </div>
              </div>
              
              <div>
                <div class="flex justify-between text-sm text-gray-600 mb-1">
                  <span>Inactive</span>
                  <span>{{ classStatusPercentage.inactive }}%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    class="bg-gray-500 h-2 rounded-full transition-all duration-300"
                    :style="{ width: `${classStatusPercentage.inactive}%` }"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Class Utilization -->
      <div v-if="dashboardStore.classUtilization.classes.length > 0" class="card">
        <div class="border-b border-gray-200 pb-4 mb-6">
          <h3 class="text-lg font-medium text-gray-900">Class Utilization</h3>
          <p class="text-sm text-gray-500 mt-1">
            Current enrollment vs capacity for active classes
          </p>
        </div>

        <!-- Overall Utilization -->
        <div class="mb-6 p-4 bg-gray-50 rounded-lg">
          <div class="flex items-center justify-between mb-2">
            <span class="text-sm font-medium text-gray-700">Overall Utilization</span>
            <span class="text-sm font-semibold text-gray-900">
              {{ Math.round(dashboardStore.classUtilization.overallUtilization) }}%
            </span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-3">
            <div 
              class="h-3 rounded-full transition-all duration-300"
              :class="getOverallCapacityColor(dashboardStore.classUtilization.overallUtilization)"
              :style="{ width: `${Math.min(dashboardStore.classUtilization.overallUtilization, 100)}%` }"
            ></div>
          </div>
          <div class="flex justify-between text-xs text-gray-500 mt-1">
            <span>{{ dashboardStore.classUtilization.totalUtilized }} enrolled</span>
            <span>{{ dashboardStore.classUtilization.totalCapacity }} capacity</span>
          </div>
        </div>

        <!-- Individual Classes -->
        <div class="space-y-4">
          <div 
            v-for="classItem in dashboardStore.classUtilization.classes" 
            :key="classItem.className"
            class="border border-gray-200 rounded-lg p-4"
          >
            <div class="flex items-center justify-between mb-2">
              <div>
                <h4 class="text-sm font-medium text-gray-900">{{ classItem.className }}</h4>
                <p class="text-xs text-gray-500">Coach: {{ classItem.coach }}</p>
              </div>
              <span class="text-sm font-semibold text-gray-900">
                {{ Math.round(classItem.utilizationPct) }}%
              </span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div 
                class="h-2 rounded-full transition-all duration-300"
                :class="getCapacityColor(classItem.utilizationPct / 100)"
                :style="{ width: `${Math.min(classItem.utilizationPct, 100)}%` }"
              ></div>
            </div>
            <div class="flex justify-between text-xs text-gray-500 mt-1">
              <span>{{ classItem.currentSize }} / {{ classItem.maxSize }} students</span>
              <span>{{ getDayName(classItem.dayOfWeek) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Coming Soon Section -->
      <div class="card">
        <div class="text-center py-8">
          <ChartPieIcon class="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h4 class="text-lg font-medium text-gray-900 mb-2">More Insights Coming Soon</h4>
          <p class="text-gray-500 mb-4">
            We're working on additional analytics including attendance tracking, revenue insights, 
            and member engagement metrics.
          </p>
          <div class="flex justify-center space-x-4 text-sm text-gray-500">
            <span>• Attendance Analytics</span>
            <span>• Revenue Tracking</span>
            <span>• Member Engagement</span>
          </div>
        </div>
      </div>
    </div>
  </LayoutComponent>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { Line } from 'vue-chartjs'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
} from 'chart.js'
import { useDashboardStore } from '@/stores/dashboard'
import LayoutComponent from '@/components/LayoutComponent.vue'
import {
  ArrowPathIcon,
  ExclamationTriangleIcon,
  XMarkIcon,
  UserPlusIcon,
  UsersIcon,
  ArrowTrendingUpIcon,
  ChartBarIcon,
  ChartPieIcon,
} from '@heroicons/vue/24/outline'

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
)

const dashboardStore = useDashboardStore()
const loading = ref(false)

// Load data on component mount
onMounted(async () => {
  await refreshData()
})

// Refresh all data
const refreshData = async () => {
  loading.value = true
  try {
    await dashboardStore.fetchAllInsights()
  } catch (error) {
    console.error('Failed to load insights data:', error)
  } finally {
    loading.value = false
  }
}

// Computed properties
const inactiveMembers = computed(() => {
  return Math.max(0, dashboardStore.stats.totalMembers - dashboardStore.stats.activeMembers)
})

const inactiveClasses = computed(() => {
  return Math.max(0, dashboardStore.stats.totalClasses - dashboardStore.stats.activeClasses)
})

const memberStatusPercentage = computed(() => {
  const total = dashboardStore.stats.totalMembers
  if (total === 0) return { active: 0, inactive: 0 }
  
  return {
    active: Math.round((dashboardStore.stats.activeMembers / total) * 100),
    inactive: Math.round((inactiveMembers.value / total) * 100)
  }
})

const classStatusPercentage = computed(() => {
  const total = dashboardStore.stats.totalClasses
  if (total === 0) return { active: 0, inactive: 0 }
  
  return {
    active: Math.round((dashboardStore.stats.activeClasses / total) * 100),
    inactive: Math.round((inactiveClasses.value / total) * 100)
  }
})

const growthTrendText = computed(() => {
  const thisMonth = dashboardStore.memberGrowth?.newMembersThisMonth || 0
  const lastMonth = dashboardStore.memberGrowth?.newMembersLastMonth || 0
  
  if (thisMonth > lastMonth) {
    return `+${thisMonth - lastMonth}`
  } else if (thisMonth < lastMonth) {
    return `${thisMonth - lastMonth}`
  } else {
    return '0'
  }
})

const growthTrendColor = computed(() => {
  const thisMonth = dashboardStore.memberGrowth?.newMembersThisMonth || 0
  const lastMonth = dashboardStore.memberGrowth?.newMembersLastMonth || 0
  
  if (thisMonth > lastMonth) {
    return 'text-green-600'
  } else if (thisMonth < lastMonth) {
    return 'text-red-600'
  } else {
    return 'text-gray-600'
  }
})

// Chart data and options
const chartData = computed(() => {
  const monthlyData = dashboardStore.memberGrowth?.monthlyGrowth || []
  
  return {
    labels: monthlyData.map(item => {
      const date = new Date(item.month + '-01')
      return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' })
    }),
    datasets: [
      {
        label: 'New Members',
        data: monthlyData.map(item => item.count),
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        borderWidth: 2,
        fill: true,
        tension: 0.4,
        pointBackgroundColor: 'rgb(59, 130, 246)',
        pointBorderColor: 'white',
        pointBorderWidth: 2,
        pointRadius: 5,
        pointHoverRadius: 7,
      }
    ]
  }
})

const chartOptions = computed(() => ({
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: false
    },
    tooltip: {
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      titleColor: 'white',
      bodyColor: 'white',
      borderColor: 'rgba(255, 255, 255, 0.1)',
      borderWidth: 1,
      cornerRadius: 8,
      displayColors: false,
      callbacks: {
        title: function(context) {
          return context[0].label
        },
        label: function(context) {
          return `${context.parsed.y} new ${context.parsed.y === 1 ? 'member' : 'members'}`
        }
      }
    }
  },
  scales: {
    x: {
      border: {
        display: false
      },
      grid: {
        display: false
      },
      ticks: {
        color: 'rgb(107, 114, 128)',
        font: {
          size: 12
        }
      }
    },
    y: {
      border: {
        display: false
      },
      grid: {
        color: 'rgba(0, 0, 0, 0.05)'
      },
      ticks: {
        color: 'rgb(107, 114, 128)',
        font: {
          size: 12
        },
        stepSize: 1,
        beginAtZero: true
      }
    }
  },
  interaction: {
    intersect: false,
    mode: 'index'
  }
}))

// Utility methods
const getCapacityColor = (ratio) => {
  if (ratio >= 0.9) return 'bg-red-500'
  if (ratio >= 0.7) return 'bg-yellow-500'
  return 'bg-green-500'
}

const getOverallCapacityColor = (percentage) => {
  if (percentage >= 90) return 'bg-red-500'
  if (percentage >= 70) return 'bg-yellow-500'
  return 'bg-green-500'
}

const getDayName = (dayOfWeek) => {
  const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
  return days[dayOfWeek] || 'Unknown'
}
</script>