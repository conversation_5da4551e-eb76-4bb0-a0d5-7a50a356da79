<!-- src/views/DashboardView.vue -->
<template>
  <LayoutComponent>
    <!-- Welcome Section -->
    <div class="mb-8">
      <div class="md:flex md:items-center md:justify-between">
        <div class="flex-1 min-w-0">
          <h1 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            Welcome back, {{ authStore.userName }}!
          </h1>
          <p class="mt-1 text-sm text-gray-500">
            Here's what's happening with your gym today.
          </p>
        </div>
        <div class="mt-4 flex md:mt-0 md:ml-4">
          <button
            @click="refreshData"
            :disabled="dashboardStore.loading"
            class="btn-secondary inline-flex items-center"
          >
            <ArrowPathIcon 
              :class="['mr-2 h-4 w-4', dashboardStore.loading ? 'animate-spin' : '']" 
            />
            Refresh
          </button>
        </div>
      </div>
    </div>

    <!-- Error Message -->
    <div v-if="dashboardStore.error" class="mb-6 alert-error">
      <div class="flex">
        <div class="flex-shrink-0">
          <ExclamationTriangleIcon class="h-5 w-5" />
        </div>
        <div class="ml-3">
          <p class="text-sm">{{ dashboardStore.error }}</p>
        </div>
        <div class="ml-auto pl-3">
          <button @click="dashboardStore.clearError" class="text-red-400 hover:text-red-600">
            <XMarkIcon class="h-5 w-5" />
          </button>
        </div>
      </div>
    </div>

    <!-- Stats Grid -->
    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
      <!-- Total Members -->
      <div class="card">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="p-3 bg-blue-100 rounded-md">
              <UsersIcon class="h-6 w-6 text-blue-600" />
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">
                Total Members
              </dt>
              <dd class="text-2xl font-semibold text-gray-900">
                {{ dashboardStore.loading ? '-' : dashboardStore.stats.totalMembers }}
              </dd>
            </dl>
          </div>
        </div>
      </div>

      <!-- Active Members -->
      <div class="card">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="p-3 bg-green-100 rounded-md">
              <CheckCircleIcon class="h-6 w-6 text-green-600" />
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">
                Active Members
              </dt>
              <dd class="text-2xl font-semibold text-gray-900">
                {{ dashboardStore.loading ? '-' : dashboardStore.stats.activeMembers }}
              </dd>
            </dl>
          </div>
        </div>
      </div>

      <!-- Total Classes -->
      <div class="card">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="p-3 bg-purple-100 rounded-md">
              <AcademicCapIcon class="h-6 w-6 text-purple-600" />
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">
                Total Classes
              </dt>
              <dd class="text-2xl font-semibold text-gray-900">
                {{ dashboardStore.loading ? '-' : dashboardStore.stats.totalClasses }}
              </dd>
            </dl>
          </div>
        </div>
      </div>

      <!-- Active Classes -->
      <div class="card">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="p-3 bg-orange-100 rounded-md">
              <ClockIcon class="h-6 w-6 text-orange-600" />
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">
                Active Classes
              </dt>
              <dd class="text-2xl font-semibold text-gray-900">
                {{ dashboardStore.loading ? '-' : dashboardStore.stats.activeClasses }}
              </dd>
            </dl>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3 mb-8">
      <div class="card">
        <div class="text-center">
          <div class="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary-100 mb-4">
            <PlusIcon class="h-6 w-6 text-primary-600" />
          </div>
          <h3 class="text-lg font-medium text-gray-900 mb-2">Add New Member</h3>
          <p class="text-sm text-gray-500 mb-4">
            Register a new member to your gym
          </p>
          <router-link
            to="/members"
            class="btn-primary w-full"
          >
            Add Member
          </router-link>
        </div>
      </div>

      <div class="card">
        <div class="text-center">
          <div class="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary-100 mb-4">
            <CalendarIcon class="h-6 w-6 text-primary-600" />
          </div>
          <h3 class="text-lg font-medium text-gray-900 mb-2">Schedule Class</h3>
          <p class="text-sm text-gray-500 mb-4">
            Add a new weekly class to your schedule
          </p>
          <router-link
            to="/classes"
            class="btn-primary w-full"
          >
            Add Class
          </router-link>
        </div>
      </div>

      <div class="card">
        <div class="text-center">
          <div class="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary-100 mb-4">
            <ChartBarIcon class="h-6 w-6 text-primary-600" />
          </div>
          <h3 class="text-lg font-medium text-gray-900 mb-2">View Insights</h3>
          <p class="text-sm text-gray-500 mb-4">
            Analyze your gym's performance
          </p>
          <router-link
            to="/insights"
            class="btn-primary w-full"
          >
            View Insights
          </router-link>
        </div>
      </div>
    </div>

    <!-- Recent Activity (Placeholder) -->
    <div class="card">
      <div class="border-b border-gray-200 pb-4 mb-4">
        <h3 class="text-lg font-medium text-gray-900">Recent Activity</h3>
      </div>
      <div class="text-center py-8">
        <InformationCircleIcon class="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <h4 class="text-lg font-medium text-gray-900 mb-2">Activity Feed Coming Soon</h4>
        <p class="text-gray-500">
          Track member check-ins, new registrations, and class updates in real-time.
        </p>
      </div>
    </div>
  </LayoutComponent>
</template>

<script setup>
import { onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useDashboardStore } from '@/stores/dashboard'
import LayoutComponent from '@/components/LayoutComponent.vue'
import {
  ArrowPathIcon,
  ExclamationTriangleIcon,
  XMarkIcon,
  UsersIcon,
  CheckCircleIcon,
  AcademicCapIcon,
  ClockIcon,
  PlusIcon,
  CalendarIcon,
  ChartBarIcon,
  InformationCircleIcon,
} from '@heroicons/vue/24/outline'

const authStore = useAuthStore()
const dashboardStore = useDashboardStore()

// Load dashboard data on component mount
onMounted(async () => {
  await refreshData()
})

// Refresh dashboard data
const refreshData = async () => {
  try {
    await dashboardStore.fetchDashboardStats()
  } catch (error) {
    console.error('Failed to load dashboard data:', error)
  }
}
</script>