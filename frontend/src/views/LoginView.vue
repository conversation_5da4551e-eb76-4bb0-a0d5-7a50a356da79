<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <!-- Header -->
      <div class="text-center">
        <div class="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary-100">
          <svg class="h-6 w-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
          </svg>
        </div>
        <h2 class="mt-6 text-3xl font-bold text-gray-900">
          Dojofy
        </h2>
        <p class="mt-2 text-sm text-gray-600">
          Sign in to manage your gym
        </p>
      </div>

      <!-- Sign In Form -->
      <div class="mt-8">
        <div class="bg-white py-8 px-6 shadow-sm rounded-lg border border-gray-200">
          <div class="space-y-6">
            <!-- Error Message -->
            <div v-if="authStore.error" class="alert-error">
              <div class="flex">
                <div class="flex-shrink-0">
                  <ExclamationTriangleIcon class="h-5 w-5" />
                </div>
                <div class="ml-3">
                  <p class="text-sm">{{ authStore.error }}</p>
                </div>
                <div class="ml-auto pl-3">
                  <button @click="authStore.clearError" class="text-red-400 hover:text-red-600">
                    <XMarkIcon class="h-5 w-5" />
                  </button>
                </div>
              </div>
            </div>

            <!-- Tab Navigation -->
            <div class="border-b border-gray-200">
              <nav class="-mb-px flex" aria-label="Tabs">
                <button
                  @click="activeTab = 'login'"
                  :class="[
                    activeTab === 'login'
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
                    'w-1/2 py-2 px-1 text-center border-b-2 font-medium text-sm'
                  ]"
                >
                  Sign In
                </button>
                <button
                  @click="activeTab = 'register'"
                  :class="[
                    activeTab === 'register'
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
                    'w-1/2 py-2 px-1 text-center border-b-2 font-medium text-sm'
                  ]"
                >
                  Register
                </button>
              </nav>
            </div>

            <!-- Login Tab -->
            <div v-if="activeTab === 'login'">
              <form @submit.prevent="handleEmailSignIn" class="space-y-4">
                <div>
                  <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
                  <input
                    id="email"
                    v-model="loginForm.email"
                    type="email"
                    required
                    class="form-input"
                    placeholder="<EMAIL>"
                  />
                </div>
                <div>
                  <label for="password" class="block text-sm font-medium text-gray-700">Password</label>
                  <input
                    id="password"
                    v-model="loginForm.password"
                    type="password"
                    required
                    class="form-input"
                    placeholder="••••••••"
                  />
                </div>
                <div class="flex items-center justify-between">
                  <div class="text-sm">
                    <button
                      type="button"
                      @click="showForgotPassword = true"
                      class="font-medium text-primary-600 hover:text-primary-500"
                    >
                      Forgot your password?
                    </button>
                  </div>
                </div>
                <div>
                  <button
                    type="submit"
                    :disabled="authStore.loading"
                    class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <div v-if="authStore.loading" class="spinner mr-3"></div>
                    Sign in
                  </button>
                </div>
              </form>

              <div class="mt-6">
                <div class="relative">
                  <div class="absolute inset-0 flex items-center">
                    <div class="w-full border-t border-gray-300"></div>
                  </div>
                  <div class="relative flex justify-center text-sm">
                    <span class="px-2 bg-white text-gray-500">Or continue with</span>
                  </div>
                </div>

                <div class="mt-6">
                  <button
                    @click="handleGoogleSignIn"
                    :disabled="authStore.loading"
                    class="group relative w-full flex justify-center py-3 px-4 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
                  >
                    <!-- Google Icon -->
                    <svg class="w-5 h-5 mr-3" viewBox="0 0 24 24">
                      <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                      <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                      <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                      <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                    </svg>
                    
                    Sign in with Google
                  </button>
                </div>
              </div>
            </div>

            <!-- Register Tab -->
            <div v-if="activeTab === 'register'">
              <form @submit.prevent="handleRegister" class="space-y-4">
                <div>
                  <label for="register-name" class="block text-sm font-medium text-gray-700">Full Name</label>
                  <input
                    id="register-name"
                    v-model="registerForm.name"
                    type="text"
                    required
                    class="form-input"
                    placeholder="John Doe"
                  />
                </div>
                <div>
                  <label for="register-email" class="block text-sm font-medium text-gray-700">Email</label>
                  <input
                    id="register-email"
                    v-model="registerForm.email"
                    type="email"
                    required
                    class="form-input"
                    placeholder="<EMAIL>"
                  />
                </div>
                <div>
                  <label for="register-password" class="block text-sm font-medium text-gray-700">Password</label>
                  <input
                    id="register-password"
                    v-model="registerForm.password"
                    type="password"
                    required
                    class="form-input"
                    placeholder="••••••••"
                  />
                  <p class="mt-1 text-xs text-gray-500">Password must be at least 6 characters</p>
                </div>
                <div>
                  <button
                    type="submit"
                    :disabled="authStore.loading || registerForm.password.length < 6"
                    class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <div v-if="authStore.loading" class="spinner mr-3"></div>
                    Create Account
                  </button>
                </div>
              </form>

              <div class="mt-6">
                <div class="relative">
                  <div class="absolute inset-0 flex items-center">
                    <div class="w-full border-t border-gray-300"></div>
                  </div>
                  <div class="relative flex justify-center text-sm">
                    <span class="px-2 bg-white text-gray-500">Or continue with</span>
                  </div>
                </div>

                <div class="mt-6">
                  <button
                    @click="handleGoogleSignIn"
                    :disabled="authStore.loading"
                    class="group relative w-full flex justify-center py-3 px-4 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
                  >
                    <!-- Google Icon -->
                    <svg class="w-5 h-5 mr-3" viewBox="0 0 24 24">
                      <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                      <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                      <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                      <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                    </svg>
                    
                    Sign up with Google
                  </button>
                </div>
              </div>
            </div>

            <!-- Features List -->
            <div class="mt-6 border-t border-gray-200 pt-6">
              <h3 class="text-sm font-medium text-gray-900 mb-3">Manage your gym with ease:</h3>
              <ul class="text-sm text-gray-600 space-y-2">
                <li class="flex items-center">
                  <CheckIcon class="h-4 w-4 text-green-500 mr-2" />
                  Track members and memberships
                </li>
                <li class="flex items-center">
                  <CheckIcon class="h-4 w-4 text-green-500 mr-2" />
                  Schedule weekly classes
                </li>
                <li class="flex items-center">
                  <CheckIcon class="h-4 w-4 text-green-500 mr-2" />
                  View detailed insights
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Forgot Password Modal -->
    <div v-if="showForgotPassword" class="fixed inset-0 z-10 overflow-y-auto">
      <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
          <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>

        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="sm:flex sm:items-start">
              <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                <h3 class="text-lg leading-6 font-medium text-gray-900">
                  Reset Password
                </h3>
                <div class="mt-2">
                  <p class="text-sm text-gray-500">
                    Enter your email address and we'll send you a link to reset your password.
                  </p>
                  <div class="mt-4">
                    <input
                      v-model="forgotPasswordEmail"
                      type="email"
                      class="form-input"
                      placeholder="<EMAIL>"
                      required
                    />
                  </div>
                  <div v-if="resetSent" class="mt-4 text-sm text-green-600">
                    Password reset email sent! Check your inbox.
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              type="button"
              @click="handleForgotPassword"
              :disabled="authStore.loading || !forgotPasswordEmail"
              class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <div v-if="authStore.loading" class="spinner mr-2"></div>
              Send Reset Link
            </button>
            <button
              type="button"
              @click="showForgotPassword = false"
              class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useRouter } from 'vue-router'
import { ExclamationTriangleIcon, XMarkIcon, CheckIcon } from '@heroicons/vue/24/outline'

const authStore = useAuthStore()
const router = useRouter()

// Component state
const activeTab = ref('login')
const showForgotPassword = ref(false)
const resetSent = ref(false)
const forgotPasswordEmail = ref('')

// Form data
const loginForm = ref({
  email: '',
  password: ''
})

const registerForm = ref({
  name: '',
  email: '',
  password: ''
})

// Handle Google sign in
const handleGoogleSignIn = async () => {
  try {
    await authStore.signIn()
    router.push({ name: 'dashboard' })
  } catch (error) {
    // Error is already handled in the store
    console.error('Sign in failed:', error)
  }
}

// Handle email/password sign in
const handleEmailSignIn = async () => {
  try {
    await authStore.signInWithEmailPassword(loginForm.value.email, loginForm.value.password)
    router.push({ name: 'dashboard' })
  } catch (error) {
    // Error is already handled in the store
    console.error('Email sign in failed:', error)
  }
}

// Handle registration
const handleRegister = async () => {
  try {
    await authStore.register(registerForm.value.email, registerForm.value.password, registerForm.value.name)
    router.push({ name: 'dashboard' })
  } catch (error) {
    // Error is already handled in the store
    console.error('Registration failed:', error)
  }
}

// Handle forgot password
const handleForgotPassword = async () => {
  try {
    await authStore.forgotPassword(forgotPasswordEmail.value)
    resetSent.value = true
    setTimeout(() => {
      showForgotPassword.value = false
      resetSent.value = false
      forgotPasswordEmail.value = ''
    }, 3000)
  } catch (error) {
    // Error is already handled in the store
    console.error('Password reset failed:', error)
  }
}
</script>
