<!-- src/views/GymSettingsView.vue -->
<template>
  <LayoutComponent>
    <!-- Header -->
    <div class="mb-8">
      <div class="md:flex md:items-center md:justify-between">
        <div class="flex-1 min-w-0">
          <h1 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            Gym Settings
          </h1>
          <p class="mt-1 text-sm text-gray-500">
            Manage your gym's information and settings.
          </p>
        </div>
        <div class="mt-4 flex md:mt-0 md:ml-4">
          <button
            @click="refreshGymInfo"
            :disabled="gymStore.loading"
            class="btn-secondary inline-flex items-center"
          >
            <ArrowPathIcon 
              :class="['mr-2 h-4 w-4', gymStore.loading ? 'animate-spin' : '']" 
            />
            Refresh
          </button>
        </div>
      </div>
    </div>

    <!-- Error Message -->
    <div v-if="gymStore.error" class="mb-6 alert-error">
      <div class="flex">
        <div class="flex-shrink-0">
          <ExclamationTriangleIcon class="h-5 w-5" />
        </div>
        <div class="ml-3">
          <p class="text-sm">{{ gymStore.error }}</p>
        </div>
        <div class="ml-auto pl-3">
          <button @click="gymStore.clearError" class="text-red-400 hover:text-red-600">
            <XMarkIcon class="h-5 w-5" />
          </button>
        </div>
      </div>
    </div>

    <!-- Success Message -->
    <div v-if="showSuccessMessage" class="mb-6 alert-success">
      <div class="flex">
        <div class="flex-shrink-0">
          <CheckCircleIcon class="h-5 w-5" />
        </div>
        <div class="ml-3">
          <p class="text-sm">Gym information updated successfully!</p>
        </div>
        <div class="ml-auto pl-3">
          <button @click="showSuccessMessage = false" class="text-green-400 hover:text-green-600">
            <XMarkIcon class="h-5 w-5" />
          </button>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="gymStore.loading && !gymStore.gym.id" class="flex justify-center py-12">
      <div class="spinner"></div>
    </div>

    <!-- Gym Information Form -->
    <div v-else class="space-y-8">
      <!-- Basic Information -->
      <div class="card">
        <div class="border-b border-gray-200 pb-4 mb-6">
          <h3 class="text-lg font-medium text-gray-900">Basic Information</h3>
          <p class="text-sm text-gray-500 mt-1">
            Update your gym's basic details and contact information.
          </p>
        </div>

        <form @submit.prevent="handleSubmit" class="space-y-6">
          <!-- Gym Name -->
          <div>
            <label class="form-label" for="gymName">
              Gym Name *
            </label>
            <input
              id="gymName"
              v-model="form.name"
              type="text"
              required
              class="form-input"
              :class="{ 'border-red-300': errors.name }"
              placeholder="Enter your gym name"
            />
            <p v-if="errors.name" class="mt-1 text-sm text-red-600">
              {{ errors.name }}
            </p>
          </div>

          <!-- Address -->
          <div>
            <label class="form-label" for="address">
              Address
            </label>
            <textarea
              id="address"
              v-model="form.address"
              rows="3"
              class="form-input"
              :class="{ 'border-red-300': errors.address }"
              placeholder="Enter your gym's full address"
            ></textarea>
            <p v-if="errors.address" class="mt-1 text-sm text-red-600">
              {{ errors.address }}
            </p>
          </div>

          <!-- Contact Information -->
          <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
            <!-- Phone -->
            <div>
              <label class="form-label" for="phone">
                Phone Number
              </label>
              <input
                id="phone"
                v-model="form.phone"
                type="tel"
                class="form-input"
                :class="{ 'border-red-300': errors.phone }"
                placeholder="(*************"
              />
              <p v-if="errors.phone" class="mt-1 text-sm text-red-600">
                {{ errors.phone }}
              </p>
            </div>

            <!-- Email -->
            <div>
              <label class="form-label" for="email">
                Contact Email
              </label>
              <input
                id="email"
                v-model="form.email"
                type="email"
                class="form-input"
                :class="{ 'border-red-300': errors.email }"
                placeholder="<EMAIL>"
              />
              <p v-if="errors.email" class="mt-1 text-sm text-red-600">
                {{ errors.email }}
              </p>
            </div>
          </div>

          <!-- Submit Button -->
          <div class="flex justify-end">
            <button
              type="submit"
              :disabled="loading"
              class="btn-primary"
            >
              <div v-if="loading" class="spinner mr-2"></div>
              Update Gym Information
            </button>
          </div>
        </form>
      </div>

      <!-- Gym Statistics -->
      <div class="card">
        <div class="border-b border-gray-200 pb-4 mb-6">
          <h3 class="text-lg font-medium text-gray-900">Gym Details</h3>
        </div>

        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="p-2 bg-blue-100 rounded-md">
                <BuildingOfficeIcon class="h-5 w-5 text-blue-600" />
              </div>
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-gray-500">Gym ID</p>
              <p class="text-sm text-gray-900 font-mono">{{ gymStore.gym.id }}</p>
            </div>
          </div>

          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="p-2 bg-green-100 rounded-md">
                <CalendarIcon class="h-5 w-5 text-green-600" />
              </div>
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-gray-500">Created</p>
              <p class="text-sm text-gray-900">{{ formatDate(gymStore.gym.createdAt) }}</p>
            </div>
          </div>

          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="p-2 bg-purple-100 rounded-md">
                <ClockIcon class="h-5 w-5 text-purple-600" />
              </div>
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-gray-500">Last Updated</p>
              <p class="text-sm text-gray-900">{{ formatDate(gymStore.gym.updatedAt) }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="card">
        <div class="border-b border-gray-200 pb-4 mb-6">
          <h3 class="text-lg font-medium text-gray-900">Quick Actions</h3>
        </div>

        <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
          <router-link
            to="/members"
            class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200"
          >
            <UsersIcon class="h-8 w-8 text-blue-600 mr-3" />
            <div>
              <p class="text-sm font-medium text-gray-900">Manage Members</p>
              <p class="text-xs text-gray-500">Add, edit, or remove members</p>
            </div>
          </router-link>

          <router-link
            to="/classes"
            class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200"
          >
            <AcademicCapIcon class="h-8 w-8 text-purple-600 mr-3" />
            <div>
              <p class="text-sm font-medium text-gray-900">Manage Classes</p>
              <p class="text-xs text-gray-500">Schedule and organize classes</p>
            </div>
          </router-link>

          <router-link
            to="/insights"
            class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200"
          >
            <ChartBarIcon class="h-8 w-8 text-green-600 mr-3" />
            <div>
              <p class="text-sm font-medium text-gray-900">View Insights</p>
              <p class="text-xs text-gray-500">Analytics and reports</p>
            </div>
          </router-link>
        </div>
      </div>
    </div>
  </LayoutComponent>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { useGymStore } from '@/stores/gym'
import LayoutComponent from '@/components/LayoutComponent.vue'
import {
  ArrowPathIcon,
  ExclamationTriangleIcon,
  XMarkIcon,
  CheckCircleIcon,
  BuildingOfficeIcon,
  CalendarIcon,
  ClockIcon,
  UsersIcon,
  AcademicCapIcon,
  ChartBarIcon,
} from '@heroicons/vue/24/outline'

const gymStore = useGymStore()

// Component state
const loading = ref(false)
const errors = ref({})
const showSuccessMessage = ref(false)

// Form data
const form = ref({
  name: '',
  address: '',
  phone: '',
  email: ''
})

// Load gym data on component mount
onMounted(async () => {
  await refreshGymInfo()
})

// Watch for gym data changes to populate form
watch(() => gymStore.gym, (newGym) => {
  if (newGym) {
    form.value = {
      name: newGym.name || '',
      address: newGym.address || '',
      phone: newGym.phone || '',
      email: newGym.email || ''
    }
  }
}, { immediate: true })

// Methods
const refreshGymInfo = async () => {
  try {
    await gymStore.fetchGymInfo()
  } catch (error) {
    console.error('Failed to load gym information:', error)
  }
}

// Form validation
const validateForm = () => {
  errors.value = {}

  if (!form.value.name.trim()) {
    errors.value.name = 'Gym name is required'
  }

  if (form.value.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.value.email)) {
    errors.value.email = 'Please enter a valid email address'
  }

  return Object.keys(errors.value).length === 0
}

// Form submission
const handleSubmit = async () => {
  if (!validateForm()) {
    return
  }

  loading.value = true
  try {
    const gymData = {
      name: form.value.name.trim(),
      address: form.value.address.trim(),
      phone: form.value.phone.trim(),
      email: form.value.email.trim()
    }

    await gymStore.updateGymInfo(gymData)
    showSuccessMessage.value = true
    
    // Hide success message after 5 seconds
    setTimeout(() => {
      showSuccessMessage.value = false
    }, 5000)
  } catch (error) {
    console.error('Failed to update gym information:', error)
  } finally {
    loading.value = false
  }
}

// Utility methods
const formatDate = (dateString) => {
  if (!dateString) return 'N/A'
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}
</script>