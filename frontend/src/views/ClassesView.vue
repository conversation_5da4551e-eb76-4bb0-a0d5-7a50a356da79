<!-- src/views/ClassesView.vue -->
<template>
  <LayoutComponent>
    <!-- Header -->
    <div class="mb-8">
      <div class="md:flex md:items-center md:justify-between">
        <div class="flex-1 min-w-0">
          <h1 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            Class Management
          </h1>
          <p class="mt-1 text-sm text-gray-500">
            Manage recurring class templates and view upcoming class instances.
          </p>
        </div>
        <div class="mt-4 flex space-x-3 md:mt-0 md:ml-4">
          <button
            @click="generateInstances"
            :disabled="classesStore.loading"
            class="btn-secondary inline-flex items-center"
          >
            <CogIcon class="mr-2 h-4 w-4" />
            Generate Instances
          </button>
          <button
            @click="refreshData"
            :disabled="classesStore.loading"
            class="btn-secondary inline-flex items-center"
          >
            <ArrowPathIcon 
              :class="['mr-2 h-4 w-4', classesStore.loading ? 'animate-spin' : '']" 
            />
            Refresh
          </button>
          <button
            @click="showCreateModal = true"
            class="btn-primary inline-flex items-center"
          >
            <PlusIcon class="mr-2 h-4 w-4" />
            Create Template
          </button>
        </div>
      </div>
    </div>

    <!-- Tab Navigation -->
    <div class="mb-6">
      <nav class="flex space-x-8" aria-label="Tabs">
        <button
          @click="activeTab = 'templates'"
          :class="[
            'whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm',
            activeTab === 'templates'
              ? 'border-primary-500 text-primary-600'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
          ]"
        >
          Class Templates ({{ classesStore.activeTemplates?.length || 0 }})
        </button>
        <button
          @click="activeTab = 'schedule'"
          :class="[
            'whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm',
            activeTab === 'schedule'
              ? 'border-primary-500 text-primary-600'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
          ]"
        >
          Upcoming Classes ({{ classesStore.upcomingInstances?.length || 0 }})
        </button>
      </nav>
    </div>

    <!-- Error Message -->
    <div v-if="classesStore.error" class="mb-6 alert-error">
      <div class="flex">
        <div class="flex-shrink-0">
          <ExclamationTriangleIcon class="h-5 w-5" />
        </div>
        <div class="ml-3">
          <p class="text-sm">{{ classesStore.error }}</p>
        </div>
        <div class="ml-auto pl-3">
          <button @click="classesStore.clearError" class="text-red-400 hover:text-red-600">
            <XMarkIcon class="h-5 w-5" />
          </button>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="classesStore.loading && classesStore.templates.length === 0" class="flex justify-center py-12">
      <div class="spinner"></div>
    </div>

    <!-- Templates Tab -->
    <div v-else-if="activeTab === 'templates'" class="space-y-6">
      <!-- Templates Grid -->
      <div v-if="classesStore.activeTemplates.length > 0" class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
        <div
          v-for="template in classesStore.activeTemplates"
          :key="template.id"
          class="card hover:shadow-md transition-shadow duration-200"
        >
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <h3 class="text-lg font-medium text-gray-900 mb-1">{{ template.name }}</h3>
              <p class="text-sm text-gray-500 mb-3">{{ template.type }}</p>
              
              <div class="space-y-2 text-sm text-gray-600">
                <div class="flex items-center">
                  <CalendarIcon class="h-4 w-4 mr-2" />
                  {{ getDayName(template.dayOfWeek) }}s
                </div>
                <div class="flex items-center">
                  <ClockIcon class="h-4 w-4 mr-2" />
                  {{ template.startTime }} - {{ template.endTime }}
                </div>
                <div class="flex items-center">
                  <UserIcon class="h-4 w-4 mr-2" />
                  {{ template.coach }}
                </div>
                <div class="flex items-center">
                  <UsersIcon class="h-4 w-4 mr-2" />
                  Max {{ template.maxCapacity }} students
                </div>
              </div>
            </div>
            
            <div class="flex space-x-2">
              <button
                @click="editTemplate(template)"
                class="text-gray-400 hover:text-gray-600"
                title="Edit template"
              >
                <PencilIcon class="h-4 w-4" />
              </button>
              <button
                @click="deleteTemplate(template)"
                class="text-gray-400 hover:text-red-600"
                title="Deactivate template"
              >
                <TrashIcon class="h-4 w-4" />
              </button>
            </div>
          </div>

          <div v-if="template.description" class="mt-3 pt-3 border-t border-gray-200">
            <p class="text-sm text-gray-600">{{ template.description }}</p>
          </div>

          <div class="mt-4 pt-3 border-t border-gray-200 flex items-center justify-between">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
              Active
            </span>
            <span class="text-xs text-gray-500">
              Since {{ formatDate(template.startDate) }}
            </span>
          </div>
        </div>
      </div>

      <!-- Empty State for Templates -->
      <div v-else class="text-center py-12">
        <AcademicCapIcon class="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <h3 class="text-lg font-medium text-gray-900 mb-2">No Class Templates</h3>
        <p class="text-gray-500 mb-4">
          Create your first recurring class template to get started.
        </p>
        <button @click="showCreateModal = true" class="btn-primary">
          Create Your First Template
        </button>
      </div>
    </div>

    <!-- Schedule Tab -->
    <div v-else-if="activeTab === 'schedule'" class="space-y-6">
      <!-- Schedule View -->
      <div v-if="classesStore.upcomingInstances.length > 0" class="space-y-4">
        <div
          v-for="instance in classesStore.upcomingInstances"
          :key="instance.id"
          class="card hover:shadow-md transition-shadow duration-200"
        >
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <div class="flex items-center space-x-4">
                <div class="flex-shrink-0">
                  <div class="p-3 bg-primary-100 rounded-lg">
                    <CalendarIcon class="h-6 w-6 text-primary-600" />
                  </div>
                </div>
                
                <div class="flex-1">
                  <h3 class="text-lg font-medium text-gray-900">{{ instance.name }}</h3>
                  <p class="text-sm text-gray-500">{{ instance.type }}</p>
                  
                  <div class="mt-2 flex items-center space-x-6 text-sm text-gray-600">
                    <div class="flex items-center">
                      <CalendarIcon class="h-4 w-4 mr-1" />
                      {{ formatInstanceDate(instance.date) }}
                    </div>
                    <div class="flex items-center">
                      <ClockIcon class="h-4 w-4 mr-1" />
                      {{ instance.startTime }} - {{ instance.endTime }}
                    </div>
                    <div class="flex items-center">
                      <UserIcon class="h-4 w-4 mr-1" />
                      {{ instance.coach }}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="flex items-center space-x-4">
              <!-- Enrollment Status -->
              <div class="text-right">
                <div class="text-sm font-medium text-gray-900">
                  {{ instance.enrolledCount || 0 }} / {{ instance.maxCapacity }}
                </div>
                <div class="text-xs text-gray-500">
                  {{ instance.waitlistCount || 0 }} waitlisted
                </div>
                <div class="mt-1">
                  <div class="w-24 bg-gray-200 rounded-full h-2">
                    <div 
                      class="h-2 rounded-full transition-all duration-300"
                      :class="getCapacityColor(instance.enrolledCount / instance.maxCapacity)"
                      :style="{ width: `${Math.min((instance.enrolledCount / instance.maxCapacity) * 100, 100)}%` }"
                    ></div>
                  </div>
                </div>
              </div>

              <!-- Status Badge -->
              <span 
                :class="getStatusBadgeClass(instance.status)"
                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
              >
                {{ instance.status }}
              </span>

              <!-- Actions -->
              <div class="flex space-x-2">
                <button
                  @click="viewInstanceDetails(instance)"
                  class="btn-secondary text-sm"
                >
                  View Details
                </button>
                <button
                  v-if="instance.status === 'scheduled'"
                  @click="cancelClassInstance(instance)"
                  class="btn-danger text-sm"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Empty State for Schedule -->
      <div v-else class="text-center py-12">
        <CalendarIcon class="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <h3 class="text-lg font-medium text-gray-900 mb-2">No Upcoming Classes</h3>
        <p class="text-gray-500 mb-4">
          Create class templates and generate instances to see upcoming classes.
        </p>
        <button @click="generateInstances" class="btn-primary">
          Generate Class Instances
        </button>
      </div>
    </div>

    <!-- Create Template Modal -->
    <div v-if="showCreateModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Create Class Template</h3>
          <form @submit.prevent="createTemplate" class="space-y-4">
            <div>
              <label class="form-label">Class Name</label>
              <input v-model="newTemplate.name" type="text" required class="form-input" />
            </div>
            <div>
              <label class="form-label">Type</label>
              <select v-model="newTemplate.type" required class="form-input">
                <option value="">Select type</option>
                <option value="Karate">Karate</option>
                <option value="BJJ">BJJ</option>
                <option value="Muay Thai">Muay Thai</option>
                <option value="Boxing">Boxing</option>
              </select>
            </div>
            <div>
              <label class="form-label">Day of Week</label>
              <select v-model="newTemplate.dayOfWeek" required class="form-input">
                <option value="">Select day</option>
                <option value="1">Monday</option>
                <option value="2">Tuesday</option>
                <option value="3">Wednesday</option>
                <option value="4">Thursday</option>
                <option value="5">Friday</option>
                <option value="6">Saturday</option>
                <option value="0">Sunday</option>
              </select>
            </div>
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="form-label">Start Time</label>
                <input v-model="newTemplate.startTime" type="time" required class="form-input" />
              </div>
              <div>
                <label class="form-label">End Time</label>
                <input v-model="newTemplate.endTime" type="time" required class="form-input" />
              </div>
            </div>
            <div>
              <label class="form-label">Coach</label>
              <input v-model="newTemplate.coach" type="text" required class="form-input" />
            </div>
            <div>
              <label class="form-label">Max Capacity</label>
              <input v-model="newTemplate.maxCapacity" type="number" min="1" required class="form-input" />
            </div>
            <div>
              <label class="form-label">Start Date</label>
              <input v-model="newTemplate.startDate" type="date" required class="form-input" />
            </div>
            <div class="flex justify-end space-x-3 pt-4">
              <button type="button" @click="showCreateModal = false" class="btn-secondary">Cancel</button>
              <button type="submit" class="btn-primary">Create Template</button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- Edit Template Modal -->
    <ClassFormModal
      v-if="showEditModal"
      :open="showEditModal"
      :classItem="templateToEdit"
      @close="showEditModal = false"
      @save="handleEditTemplate"
    />

    <!-- Cancel Instance Modal -->
    <div v-if="showCancelModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Cancel Class</h3>
          <p class="text-gray-600 mb-4">
            Are you sure you want to cancel the "{{ instanceToCancel?.name }}" class on {{ formatInstanceDate(instanceToCancel?.date) }}?
          </p>
          <div>
            <label class="form-label">Reason for Cancellation</label>
            <textarea v-model="cancellationReason" class="form-input" rows="3" placeholder="Provide a reason for cancellation"></textarea>
          </div>
          <div class="flex justify-end space-x-3 pt-4">
            <button type="button" @click="showCancelModal = false" class="btn-secondary">No, Keep It</button>
            <button type="button" @click="confirmCancelInstance" class="btn-danger">Yes, Cancel Class</button>
          </div>
        </div>
      </div>
    </div>
  </LayoutComponent>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useClassesStore } from '@/stores/classes'
import LayoutComponent from '@/components/LayoutComponent.vue'
import ClassFormModal from '@/components/ClassFormModal.vue'
import {
  ArrowPathIcon,
  PlusIcon,
  ExclamationTriangleIcon,
  XMarkIcon,
  CalendarIcon,
  ClockIcon,
  UserIcon,
  UsersIcon,
  AcademicCapIcon,
  PencilIcon,
  TrashIcon,
  CogIcon,
} from '@heroicons/vue/24/outline'

const classesStore = useClassesStore()

// Component state
const activeTab = ref('templates')
const showCreateModal = ref(false)
const showEditModal = ref(false)
const templateToEdit = ref(null)
const newTemplate = ref({
  name: '',
  type: '',
  dayOfWeek: '',
  startTime: '',
  endTime: '',
  coach: '',
  maxCapacity: 20,
  startDate: ''
})
const showCancelModal = ref(false)
const instanceToCancel = ref(null)
const cancellationReason = ref('')

// Load data on component mount
onMounted(async () => {
  await refreshData()
})

// Methods
const refreshData = async () => {
  try {
    await classesStore.fetchClassSchedule(4)
  } catch (error) {
    console.error('Failed to load class data:', error)
  }
}

const generateInstances = async () => {
  try {
    await classesStore.generateInstances(8)
  } catch (error) {
    console.error('Failed to generate instances:', error)
  }
}

const createTemplate = async () => {
  try {
    console.log('Form data before submission:', newTemplate.value)
    
    // Ensure proper data types
    const templateData = {
      ...newTemplate.value,
      dayOfWeek: parseInt(newTemplate.value.dayOfWeek),
      maxSize: parseInt(newTemplate.value.maxCapacity), // Change maxCapacity to maxSize
      startDate: new Date(newTemplate.value.startDate).toISOString()
    }
    
    console.log('Formatted template data:', templateData)
    await classesStore.createClassTemplate(templateData)
    showCreateModal.value = false
    resetForm()
    await refreshData()
  } catch (error) {
    console.error('Failed to create template:', error)
  }
}

const editTemplate = (template) => {
  templateToEdit.value = template
  showEditModal.value = true
}

const deleteTemplate = async (template) => {
  if (confirm(`Deactivate "${template.name}"?`)) {
    try {
      await classesStore.deleteClassTemplate(template.id)
    } catch (error) {
      console.error('Failed to delete template:', error)
    }
  }
}

const viewInstanceDetails = (instance) => {
  console.log('View instance details:', instance)
}

const resetForm = () => {
  newTemplate.value = {
    name: '',
    type: '',
    dayOfWeek: '',
    startTime: '',
    endTime: '',
    coach: '',
    maxCapacity: 20, // Keep this as maxCapacity for the form
    startDate: ''
  }
}

const handleEditTemplate = async (updatedData) => {
  try {
    await classesStore.updateClassTemplate(templateToEdit.value.id, updatedData)
    showEditModal.value = false
    templateToEdit.value = null
    await refreshData()
  } catch (error) {
    console.error('Failed to update template:', error)
  }
}

const cancelClassInstance = (instance) => {
  instanceToCancel.value = instance
  cancellationReason.value = ''
  showCancelModal.value = true
}

const confirmCancelInstance = async () => {
  try {
    await classesStore.cancelClassInstance(instanceToCancel.value.id, cancellationReason.value)
    showCancelModal.value = false
    instanceToCancel.value = null
    cancellationReason.value = ''
    await refreshData()
  } catch (error) {
    console.error('Failed to cancel class instance:', error)
  }
}

// Utility methods
const getDayName = (dayOfWeek) => {
  const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
  return days[dayOfWeek] || 'Unknown'
}

const formatDate = (dateString) => {
  if (!dateString) return 'N/A'
  return new Date(dateString).toLocaleDateString()
}

const formatInstanceDate = (dateString) => {
  if (!dateString) return 'N/A'
  return new Date(dateString).toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const getCapacityColor = (ratio) => {
  if (ratio >= 0.9) return 'bg-red-500'
  if (ratio >= 0.7) return 'bg-yellow-500'
  return 'bg-green-500'
}

const getStatusBadgeClass = (status) => {
  const classes = {
    scheduled: 'bg-blue-100 text-blue-800',
    completed: 'bg-green-100 text-green-800',
    cancelled: 'bg-red-100 text-red-800',
    in_progress: 'bg-yellow-100 text-yellow-800'
  }
  return classes[status] || 'bg-gray-100 text-gray-800'
}
</script>
