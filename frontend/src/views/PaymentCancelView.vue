<template>
  <div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
      <div class="flex justify-center">
        <XCircleIcon class="h-16 w-16 text-red-500" />
      </div>
      <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
        Payment Cancelled
      </h2>
      <p class="mt-2 text-center text-sm text-gray-600">
        Your payment was not completed.
      </p>
    </div>

    <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
      <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
        <div class="space-y-6">
          <div>
            <p class="text-sm text-gray-700">
              You have cancelled the payment process. Your membership has not been activated.
              If you encountered any issues, please contact support or try again.
            </p>
          </div>
          
          <div class="flex justify-center space-x-4">
            <router-link to="/dashboard" class="btn-secondary">
              Go to Dashboard
            </router-link>
            <router-link to="/members" class="btn-primary">
              Try Again
            </router-link>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { XCircleIcon } from '@heroicons/vue/24/outline'
</script>