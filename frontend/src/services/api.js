// src/services/api.js
import { auth } from './firebase'

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080'

// API client class for making HTTP requests to the backend
class ApiClient {
  constructor() {
    this.baseURL = `${API_BASE_URL}/api/v1`
  }

  // Get the current user's ID token for authentication
  async getAuthToken() {
    const user = auth.currentUser
    if (!user) {
      throw new Error('User not authenticated')
    }
    return await user.getIdToken()
  }

  // Make authenticated HTTP requests
  async request(endpoint, options = {}) {
    try {
      const token = await this.getAuthToken()
      
      const config = {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
          ...options.headers,
        },
        ...options,
      }

      // Debug log
      console.log(`API Request to ${endpoint}:`, {
        url: `${this.baseURL}${endpoint}`,
        method: options.method || 'GET',
        headers: config.headers,
        body: options.body ? JSON.parse(options.body) : undefined
      })

      const response = await fetch(`${this.baseURL}${endpoint}`, config)
      
      if (!response.ok) {
        // Debug log for error responses
        const errorText = await response.text()
        console.error(`API Error (${response.status}):`, errorText)
        
        let error
        try {
          error = JSON.parse(errorText)
        } catch {
          error = { error: errorText || 'Request failed' }
        }
        
        throw new Error(error.error || `HTTP ${response.status}`)
      }

      return await response.json()
    } catch (err) {
      console.error(`API request failed for ${endpoint}:`, err)
      throw new Error(err.message || 'Failed to fetch')
    }
  }

  // GET request
  async get(endpoint) {
    return this.request(endpoint, { method: 'GET' })
  }

  // POST request
  async post(endpoint, data) {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  // PUT request
  async put(endpoint, data) {
    return this.request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
    })
  }

  // DELETE request
  async delete(endpoint) {
    return this.request(endpoint, { method: 'DELETE' })
  }

  // Authentication endpoints
  async verifyToken(idToken) {
    const response = await fetch(`${this.baseURL}/auth/verify`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ idToken }),
    })

    if (!response.ok) {
      const error = await response.json().catch(() => ({ error: 'Token verification failed' }))
      throw new Error(error.error || 'Authentication failed')
    }

    return await response.json()
  }

  async verifyInvitation(memberId, accessCode) {
    return this.post('/auth/verify-invitation', { memberId, accessCode })
  }

  async linkMemberAccount(memberId, accessCode, userUid) {
    return this.post('/auth/link-member-account', { memberId, accessCode, userUid })
  }

  // Dashboard endpoints
  async getDashboardStats() {
    return this.get('/dashboard/stats')
  }

  // Member endpoints
  async getMembers() {
    return this.get('/members')
  }

  async getMember(id) {
    return this.get(`/members/${id}`)
  }

  async createMember(memberData) {
    return this.post('/members', memberData)
  }

  async updateMember(id, memberData) {
    return this.put(`/members/${id}`, memberData)
  }

  async deleteMember(id) {
    return this.delete(`/members/${id}`)
  }

  async sendMemberInvitation(id) {
    return this.post(`/members/${id}/invite`)
  }

  // Class endpoints
  async getClasses() {
    return this.get('/classes')
  }

  async getClass(id) {
    return this.get(`/classes/${id}`)
  }

  async createClass(classData) {
    return this.post('/classes', classData)
  }

  async updateClass(id, classData) {
    return this.put(`/classes/${id}`, classData)
  }

  async deleteClass(id) {
    return this.delete(`/classes/${id}`)
  }

  async getClassInstances(weeksAhead = 4) {
    return this.get(`/classes/instances?weeks=${weeksAhead}`)
  }

  async generateClassInstances(weeksAhead = 8) {
    return this.post('/classes/generate-instances', { weeksAhead })
  }

  async updateClassInstance(id, instanceData) {
    return this.put(`/classes/instances/${id}`, instanceData)
  }

  async cancelClassInstance(id, reason) {
    return this.put(`/classes/instances/${id}/cancel`, { reason })
  }

  // Gym management endpoints
  async getGymInfo() {
    return this.get('/gym')
  }

  async updateGymInfo(gymData) {
    return this.put('/gym', gymData)
  }

  // Insights endpoints
  async getAllInsights() {
    return this.get('/insights')
  }

  async getNewMembersThisMonth() {
    return this.get('/insights/new-members')
  }

  async getMemberGrowthStats() {
    return this.get('/insights/member-growth')
  }

  // Membership plan endpoints
  async getMembershipPlans() {
    return this.get('/memberships')
  }

  async getMembershipPlan(id) {
    return this.get(`/memberships/${id}`)
  }

  async createMembershipPlan(planData) {
    return this.post('/memberships', planData)
  }

  async updateMembershipPlan(id, planData) {
    return this.put(`/memberships/${id}`, planData)
  }

  async deleteMembershipPlan(id) {
    return this.delete(`/memberships/${id}`)
  }
}

// Export a singleton instance
export const apiClient = new ApiClient()
