// src/services/firebase.js
import { initializeApp } from 'firebase/app'
import { 
  getAuth,
  connectAuthEmulator,
  GoogleAuthProvider, 
  signInWithPopup, 
  signOut,
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  sendPasswordResetEmail,
  updateProfile
} from 'firebase/auth'
import { getFirestore } from 'firebase/firestore'

// Firebase configuration from environment variables
const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: import.meta.env.VITE_FIREBASE_APP_ID
}

// Initialize Firebase
const app = initializeApp(firebaseConfig)

// Initialize Firebase Auth
const auth = getAuth(app)

if (import.meta.env.DEV) {
  try {
    connectAuthEmulator(auth, "http://localhost:9099");
    console.log("🔥 Auth emulator connected");
  } catch (error) {
    console.error("Error connecting to auth emulator:", error);
  }
}

// Initialize Firestore
const db = getFirestore(app)

// Google Auth Provider
const googleProvider = new GoogleAuthProvider()

// Auth helper functions
export const signInWithGoogle = () => {
  return signInWithPopup(auth, googleProvider)
}

// Email/Password Authentication
export const registerWithEmail = (email, password, displayName) => {
  return createUserWithEmailAndPassword(auth, email, password)
    .then((result) => {
      // Update the user profile with display name
      return updateProfile(result.user, { displayName })
        .then(() => result)
    })
}

export const signInWithEmail = (email, password) => {
  return signInWithEmailAndPassword(auth, email, password)
}

export const resetPassword = (email) => {
  return sendPasswordResetEmail(auth, email)
}

export const logOut = () => {
  return signOut(auth)
}

export { auth, db }
