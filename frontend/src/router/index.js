// src/router/index.js
import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

// Lazy load components for better performance
const LoginView = () => import('@/views/LoginView.vue')
const DashboardView = () => import('@/views/DashboardView.vue')
const MembersView = () => import('@/views/MembersView.vue')
const ClassesView = () => import('@/views/ClassesView.vue')
const InsightsView = () => import('@/views/InsightsView.vue')
const GymSettingsView = () => import('@/views/GymSettingsView.vue')

const routes = [
  {
    path: '/',
    name: 'login',
    component: LoginView,
    meta: { requiresGuest: true }
  },
  {
    path: '/dashboard',
    name: 'dashboard',
    component: DashboardView,
    meta: { requiresAuth: true, requiresOwner: true }
  },
  {
    path: '/members',
    name: 'members',
    component: MembersView,
    meta: { requiresAuth: true, requiresOwner: true }
  },
  {
    path: '/classes',
    name: 'classes',
    component: ClassesView,
    meta: { requiresAuth: true, requiresOwner: true }
  },
  {
    path: '/insights',
    name: 'insights',
    component: InsightsView,
    meta: { requiresAuth: true, requiresOwner: true }
  },
  {
    path: '/gym-settings',
    name: 'gym-settings',
    component: GymSettingsView,
    meta: { requiresAuth: true, requiresOwner: true }
  },
  {
    path: '/memberships',
    name: 'memberships',
    component: () => import('@/views/MembershipsView.vue'),
    meta: { requiresAuth: true, requiresOwner: true }
  },
  {
    path: '/register',
    name: 'register',
    component: () => import('../views/MemberRegistration.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/member-dashboard',
    name: 'member-dashboard',
    component: () => import('@/views/MemberDashboardView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/payment/success',
    name: 'PaymentSuccess',
    component: () => import('@/views/PaymentSuccessView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/payment/cancel',
    name: 'PaymentCancel',
    component: () => import('@/views/PaymentCancelView.vue'),
    meta: { requiresAuth: true }
  },
  {
    // Catch all route - redirect to login
    path: '/:pathMatch(.*)*',
    redirect: '/'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// Navigation guards
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()
  
  // Wait for auth to initialize on first load
  if (authStore.loading) {
    await authStore.initializeAuth()
  }

  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)
  const requiresGuest = to.matched.some(record => record.meta.requiresGuest)
  const requiresOwner = to.matched.some(record => record.meta.requiresOwner)

  if (requiresAuth && !authStore.isAuthenticated) {
    // Redirect to login if authentication is required
    next({ name: 'login' })
  } else if (requiresGuest && authStore.isAuthenticated) {
    // Redirect to dashboard if already authenticated
    if (authStore.isMember) {
      next({ name: 'member-dashboard' })
    } else {
      next({ name: 'dashboard' })
    }
  } else if (requiresOwner && authStore.isMember) {
    // Redirect members away from owner-only pages
    next({ name: 'member-dashboard' })
  } else {
    next()
  }
})

export default router
