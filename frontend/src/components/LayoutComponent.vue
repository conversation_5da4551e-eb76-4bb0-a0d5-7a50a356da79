<!-- src/components/LayoutComponent.vue -->
<template>
  <div class="min-h-screen bg-gray-50 flex">
    <!-- Sidebar -->
    <div class="hidden md:flex md:w-64 md:flex-col">
      <div class="flex flex-col flex-grow pt-5 pb-4 overflow-y-auto bg-white border-r border-gray-200">
        <!-- Logo -->
        <div class="flex items-center flex-shrink-0 px-4">
          <div class="h-8 w-8 flex items-center justify-center rounded-lg bg-primary-100">
            <svg class="h-5 w-5 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          </div>
          <span class="ml-3 text-lg font-semibold text-gray-900">Gym Manager</span>
        </div>

        <!-- Navigation -->
        <nav class="mt-8 flex-1 px-4 space-y-1">
          <router-link
            v-for="item in navigation"
            :key="item.name"
            :to="item.href"
            :class="[
              item.current
                ? 'bg-primary-50 border-primary-500 text-primary-700'
                : 'border-transparent text-gray-600 hover:bg-gray-50 hover:text-gray-900',
              'group flex items-center px-3 py-2 text-sm font-medium border-l-4 transition-colors duration-200'
            ]"
          >
            <component
              :is="item.icon"
              :class="[
                item.current ? 'text-primary-500' : 'text-gray-400 group-hover:text-gray-500',
                'mr-3 h-5 w-5 transition-colors duration-200'
              ]"
            />
            {{ item.name }}
          </router-link>
        </nav>
      </div>
    </div>

    <!-- Mobile sidebar -->
    <TransitionRoot as="template" :show="sidebarOpen">
      <Dialog as="div" class="relative z-40 md:hidden" @close="sidebarOpen = false">
        <TransitionChild
          as="template"
          enter="transition-opacity ease-linear duration-300"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="transition-opacity ease-linear duration-300"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-gray-600 bg-opacity-75" />
        </TransitionChild>

        <div class="fixed inset-0 flex z-40">
          <TransitionChild
            as="template"
            enter="transition ease-in-out duration-300 transform"
            enter-from="-translate-x-full"
            enter-to="translate-x-0"
            leave="transition ease-in-out duration-300 transform"
            leave-from="translate-x-0"
            leave-to="-translate-x-full"
          >
            <DialogPanel class="relative flex-1 flex flex-col max-w-xs w-full bg-white">
              <TransitionChild
                as="template"
                enter="ease-in-out duration-300"
                enter-from="opacity-0"
                enter-to="opacity-100"
                leave="ease-in-out duration-300"
                leave-from="opacity-100"
                leave-to="opacity-0"
              >
                <div class="absolute top-0 right-0 -mr-12 pt-2">
                  <button
                    type="button"
                    class="ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
                    @click="sidebarOpen = false"
                  >
                    <span class="sr-only">Close sidebar</span>
                    <XMarkIcon class="h-6 w-6 text-white" />
                  </button>
                </div>
              </TransitionChild>
              
              <div class="flex-1 h-0 pt-5 pb-4 overflow-y-auto">
                <div class="flex items-center flex-shrink-0 px-4">
                  <div class="h-8 w-8 flex items-center justify-center rounded-lg bg-primary-100">
                    <svg class="h-5 w-5 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                  <span class="ml-3 text-lg font-semibold text-gray-900">Gym Manager</span>
                </div>
                <nav class="mt-5 px-2 space-y-1">
                  <router-link
                    v-for="item in navigation"
                    :key="item.name"
                    :to="item.href"
                    :class="[
                      item.current
                        ? 'bg-primary-50 text-primary-700'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900',
                      'group flex items-center px-2 py-2 text-base font-medium rounded-md transition-colors duration-200'
                    ]"
                    @click="sidebarOpen = false"
                  >
                    <component
                      :is="item.icon"
                      :class="[
                        item.current ? 'text-primary-500' : 'text-gray-400 group-hover:text-gray-500',
                        'mr-4 h-6 w-6 transition-colors duration-200'
                      ]"
                    />
                    {{ item.name }}
                  </router-link>
                </nav>
              </div>
            </DialogPanel>
          </TransitionChild>
          <div class="flex-shrink-0 w-14"></div>
        </div>
      </Dialog>
    </TransitionRoot>

    <!-- Main content -->
    <div class="flex flex-col w-0 flex-1 overflow-hidden">
      <!-- Top bar -->
      <div class="relative z-10 flex-shrink-0 flex h-16 bg-white shadow border-b border-gray-200">
        <button
          type="button"
          class="px-4 border-r border-gray-200 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500 md:hidden"
          @click="sidebarOpen = true"
        >
          <span class="sr-only">Open sidebar</span>
          <Bars3Icon class="h-6 w-6" />
        </button>
        
        <div class="flex-1 px-4 flex justify-between items-center">
          <div class="flex-1 flex">
            <h1 class="text-lg font-semibold text-gray-900">{{ pageTitle }}</h1>
          </div>
          
          <!-- User menu -->
          <div class="ml-4 flex items-center md:ml-6">
            <Menu as="div" class="ml-3 relative">
              <div>
                <MenuButton class="max-w-xs bg-white flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                  <span class="sr-only">Open user menu</span>
                  <img
                    v-if="authStore.user?.photoURL"
                    class="h-8 w-8 rounded-full"
                    :src="authStore.user.photoURL"
                    :alt="authStore.user.displayName"
                  >
                  <div
                    v-else
                    class="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center"
                  >
                    <UserIcon class="h-5 w-5 text-gray-500" />
                  </div>
                </MenuButton>
              </div>
              <transition
                enter-active-class="transition ease-out duration-100"
                enter-from-class="transform opacity-0 scale-95"
                enter-to-class="transform opacity-100 scale-100"
                leave-active-class="transition ease-in duration-75"
                leave-from-class="transform opacity-100 scale-100"
                leave-to-class="transform opacity-0 scale-95"
              >
                <MenuItems class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg py-1 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none">
                  <div class="px-4 py-2 text-sm text-gray-700 border-b border-gray-100">
                    <p class="font-medium">{{ authStore.userName }}</p>
                    <p class="text-gray-500">{{ authStore.userEmail }}</p>
                  </div>
                  <MenuItem v-slot="{ active }">
                    <button
                      @click="handleSignOut"
                      :class="[
                        active ? 'bg-gray-100' : '',
                        'block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100'
                      ]"
                    >
                      Sign out
                    </button>
                  </MenuItem>
                </MenuItems>
              </transition>
            </Menu>
          </div>
        </div>
      </div>

      <!-- Page content -->
      <main class="flex-1 relative overflow-y-auto focus:outline-none">
        <div class="py-6">
          <div class="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
            <slot />
          </div>
        </div>
      </main>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import {
  Dialog,
  DialogPanel,
  Menu,
  MenuButton,
  MenuItem,
  MenuItems,
  TransitionChild,
  TransitionRoot,
} from '@headlessui/vue'
import {
  Bars3Icon,
  XMarkIcon,
  UserIcon,
  HomeIcon,
  UsersIcon,
  AcademicCapIcon,
  ChartBarIcon,
  CogIcon,
  CreditCardIcon,
} from '@heroicons/vue/24/outline'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

const sidebarOpen = ref(false)

// Navigation items
const navigation = computed(() => [
  {
    name: 'Dashboard',
    href: '/dashboard',
    icon: HomeIcon,
    current: route.name === 'dashboard'
  },
  {
    name: 'Members',
    href: '/members',
    icon: UsersIcon,
    current: route.name === 'members'
  },
  {
    name: 'Classes',
    href: '/classes',
    icon: AcademicCapIcon,
    current: route.name === 'classes'
  },
  {
    name: 'Memberships',
    href: '/memberships',
    icon: CreditCardIcon,
    current: route.name === 'memberships'
  },
  {
    name: 'Insights',
    href: '/insights',
    icon: ChartBarIcon,
    current: route.name === 'insights'
  },
  {
    name: 'Gym Settings',
    href: '/gym-settings',
    icon: CogIcon,
    current: route.name === 'gym-settings'
  }
])

// Page title based on current route
const pageTitle = computed(() => {
  const titles = {
    dashboard: 'Dashboard',
    members: 'Members',
    classes: 'Classes',
    insights: 'Insights',
    'gym-settings': 'Gym Settings'
  }
  return titles[route.name] || 'Gym Manager'
})

// Handle sign out
const handleSignOut = async () => {
  try {
    await authStore.signOut()
    router.push({ name: 'login' })
  } catch (error) {
    console.error('Sign out failed:', error)
  }
}
</script>
