<template>
  <TransitionRoot appear :show="open" as="template">
    <Dialog as="div" @close="onClose" class="relative z-10">
      <TransitionChild
        as="template"
        enter="ease-out duration-300"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="ease-in duration-200"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
      </TransitionChild>

      <div class="fixed inset-0 z-10 overflow-y-auto">
        <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
          <TransitionChild
            as="template"
            enter="ease-out duration-300"
            enter-from="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            enter-to="opacity-100 translate-y-0 sm:scale-100"
            leave="ease-in duration-200"
            leave-from="opacity-100 translate-y-0 sm:scale-100"
            leave-to="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
          >
            <DialogPanel class="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
              <div>
                <DialogTitle as="h3" class="text-lg font-medium leading-6 text-gray-900">
                  {{ isEditing ? 'Edit Membership Plan' : 'Create Membership Plan' }}
                </DialogTitle>
                <div class="mt-4">
                  <form @submit.prevent="handleSubmit" class="space-y-4">
                    <!-- Name -->
                    <div>
                      <label for="name" class="block text-sm font-medium text-gray-700">Name</label>
                      <input
                        type="text"
                        id="name"
                        v-model="form.name"
                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                        :class="{ 'border-red-500': errors.name }"
                        required
                      />
                      <p v-if="errors.name" class="mt-1 text-sm text-red-600">{{ errors.name }}</p>
                    </div>

                    <!-- Description -->
                    <div>
                      <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
                      <textarea
                        id="description"
                        v-model="form.description"
                        rows="3"
                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                      ></textarea>
                    </div>

                    <!-- Price and Currency -->
                    <div class="grid grid-cols-2 gap-4">
                      <div>
                        <label for="price" class="block text-sm font-medium text-gray-700">Price</label>
                        <input
                          type="number"
                          id="price"
                          v-model="form.price"
                          min="0"
                          step="0.01"
                          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                          :class="{ 'border-red-500': errors.price }"
                          required
                        />
                        <p v-if="errors.price" class="mt-1 text-sm text-red-600">{{ errors.price }}</p>
                      </div>
                      <div>
                        <label for="currency" class="block text-sm font-medium text-gray-700">Currency</label>
                        <select
                          id="currency"
                          v-model="form.currency"
                          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                          :class="{ 'border-red-500': errors.currency }"
                          required
                        >
                          <option value="">Select currency</option>
                          <option value="USD">USD</option>
                          <option value="EUR">EUR</option>
                          <option value="GBP">GBP</option>
                          <option value="CAD">CAD</option>
                          <option value="AUD">AUD</option>
                        </select>
                        <p v-if="errors.currency" class="mt-1 text-sm text-red-600">{{ errors.currency }}</p>
                      </div>
                    </div>

                    <!-- Billing Cycle -->
                    <div>
                      <label for="billingCycle" class="block text-sm font-medium text-gray-700">Billing Cycle</label>
                      <select
                        id="billingCycle"
                        v-model="form.billingCycle"
                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                        :class="{ 'border-red-500': errors.billingCycle }"
                        required
                      >
                        <option value="">Select billing cycle</option>
                        <option value="monthly">Monthly</option>
                        <option value="quarterly">Quarterly</option>
                        <option value="yearly">Yearly</option>
                      </select>
                      <p v-if="errors.billingCycle" class="mt-1 text-sm text-red-600">{{ errors.billingCycle }}</p>
                    </div>

                    <!-- Classes Per Week -->
                    <div>
                      <label for="classesPerWeek" class="block text-sm font-medium text-gray-700">
                        Classes Per Week (0 for unlimited)
                      </label>
                      <input
                        type="number"
                        id="classesPerWeek"
                        v-model="form.classesPerWeek"
                        min="0"
                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                      />
                    </div>

                    <!-- Includes Private -->
                    <div class="flex items-start">
                      <div class="flex h-5 items-center">
                        <input
                          id="includesPrivate"
                          type="checkbox"
                          v-model="form.includesPrivate"
                          class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                        />
                      </div>
                      <div class="ml-3 text-sm">
                        <label for="includesPrivate" class="font-medium text-gray-700">Includes Private Lessons</label>
                        <p class="text-gray-500">Check if this plan includes private lessons</p>
                      </div>
                    </div>

                    <!-- Active Status (only for editing) -->
                    <div v-if="isEditing" class="flex items-start">
                      <div class="flex h-5 items-center">
                        <input
                          id="isActive"
                          type="checkbox"
                          v-model="form.isActive"
                          class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                        />
                      </div>
                      <div class="ml-3 text-sm">
                        <label for="isActive" class="font-medium text-gray-700">Active</label>
                        <p class="text-gray-500">Inactive plans won't be available for new members</p>
                      </div>
                    </div>

                    <div class="mt-5 sm:mt-6 sm:grid sm:grid-flow-row-dense sm:grid-cols-2 sm:gap-3">
                      <button
                        type="submit"
                        class="inline-flex w-full justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-base font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:col-start-2 sm:text-sm"
                        :disabled="loading"
                      >
                        {{ loading ? 'Saving...' : 'Save' }}
                      </button>
                      <button
                        type="button"
                        class="mt-3 inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:col-start-1 sm:mt-0 sm:text-sm"
                        @click="onClose"
                        :disabled="loading"
                      >
                        Cancel
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue'

// Props
const props = defineProps({
  open: {
    type: Boolean,
    default: false
  },
  plan: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['close', 'save'])

// Component state
const loading = ref(false)
const errors = ref({})

// Form data
const form = ref({
  name: '',
  description: '',
  price: 0,
  currency: '',
  billingCycle: '',
  classesPerWeek: 0,
  includesPrivate: false,
  isActive: true
})

// Computed
const isEditing = computed(() => !!props.plan)

// Methods
const resetForm = () => {
  form.value = {
    name: '',
    description: '',
    price: 0,
    currency: '',
    billingCycle: '',
    classesPerWeek: 0,
    includesPrivate: false,
    isActive: true
  }
  errors.value = {}
}

// Watchers
watch(() => props.plan, (newPlan) => {
  if (newPlan) {
    form.value = {
      name: newPlan.name || '',
      description: newPlan.description || '',
      price: newPlan.price || 0,
      currency: newPlan.currency || '',
      billingCycle: newPlan.billingCycle || '',
      classesPerWeek: newPlan.classesPerWeek || 0,
      includesPrivate: newPlan.includesPrivate || false,
      isActive: newPlan.isActive !== undefined ? newPlan.isActive : true
    }
  } else {
    resetForm()
  }
}, { immediate: true })

const validateForm = () => {
  errors.value = {}
  let isValid = true

  if (!form.value.name.trim()) {
    errors.value.name = 'Name is required'
    isValid = false
  }

  if (!form.value.price && form.value.price !== 0) {
    errors.value.price = 'Price is required'
    isValid = false
  }

  if (!form.value.currency) {
    errors.value.currency = 'Currency is required'
    isValid = false
  }

  if (!form.value.billingCycle) {
    errors.value.billingCycle = 'Billing cycle is required'
    isValid = false
  }

  return isValid
}

const onClose = () => {
  emit('close')
}

// Form submission
const handleSubmit = async () => {
  if (!validateForm()) {
    return
  }

  loading.value = true
  try {
    const planData = {
      name: form.value.name.trim(),
      description: form.value.description.trim(),
      price: parseFloat(form.value.price),
      currency: form.value.currency,
      billingCycle: form.value.billingCycle,
      classesPerWeek: parseInt(form.value.classesPerWeek),
      includesPrivate: form.value.includesPrivate
    }

    // Include isActive only for editing
    if (isEditing.value) {
      planData.isActive = form.value.isActive
    }

    emit('save', planData)
  } catch (error) {
    console.error('Form submission error:', error)
  } finally {
    loading.value = false
  }
}
</script>
