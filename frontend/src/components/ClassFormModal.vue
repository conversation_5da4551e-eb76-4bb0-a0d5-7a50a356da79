<!-- src/components/ClassFormModal.vue -->
<template>
  <TransitionRoot as="template" :show="open">
    <Dialog as="div" class="relative z-50" @close="$emit('close')">
      <TransitionChild
        as="template"
        enter="ease-out duration-300"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="ease-in duration-200"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
      </TransitionChild>

      <div class="fixed inset-0 z-10 overflow-y-auto">
        <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
          <TransitionChild
            as="template"
            enter="ease-out duration-300"
            enter-from="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            enter-to="opacity-100 translate-y-0 sm:scale-100"
            leave="ease-in duration-200"
            leave-from="opacity-100 translate-y-0 sm:scale-100"
            leave-to="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
          >
            <DialogPanel class="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
              <form @submit.prevent="handleSubmit">
                <div>
                  <div class="mt-3 text-center sm:mt-0 sm:text-left">
                    <DialogTitle as="h3" class="text-lg font-semibold leading-6 text-gray-900">
                      {{ isEditing ? 'Edit Class' : 'Add New Class' }}
                    </DialogTitle>
                    <div class="mt-6 space-y-4">
                      <!-- Class Name -->
                      <div>
                        <label class="form-label" for="name">
                          Class Name *
                        </label>
                        <input
                          id="name"
                          v-model="form.name"
                          type="text"
                          required
                          class="form-input"
                          :class="{ 'border-red-300': errors.name }"
                          placeholder="e.g., Advanced Karate, Beginner BJJ"
                        />
                        <p v-if="errors.name" class="mt-1 text-sm text-red-600">
                          {{ errors.name }}
                        </p>
                      </div>

                      <!-- Class Type -->
                      <div>
                        <label class="form-label" for="type">
                          Class Type *
                        </label>
                        <select
                          id="type"
                          v-model="form.type"
                          required
                          class="form-input"
                          :class="{ 'border-red-300': errors.type }"
                        >
                          <option value="">Select class type</option>
                          <option value="Karate">Karate</option>
                          <option value="Taekwondo">Taekwondo</option>
                          <option value="Brazilian Jiu-Jitsu">Brazilian Jiu-Jitsu</option>
                          <option value="Muay Thai">Muay Thai</option>
                          <option value="Boxing">Boxing</option>
                          <option value="Kickboxing">Kickboxing</option>
                          <option value="Judo">Judo</option>
                          <option value="MMA">MMA</option>
                          <option value="Kung Fu">Kung Fu</option>
                          <option value="Self Defense">Self Defense</option>
                          <option value="Fitness">Fitness</option>
                          <option value="Other">Other</option>
                        </select>
                        <p v-if="errors.type" class="mt-1 text-sm text-red-600">
                          {{ errors.type }}
                        </p>
                      </div>

                      <!-- Day of Week -->
                      <div>
                        <label class="form-label" for="dayOfWeek">
                          Day of Week *
                        </label>
                        <select
                          id="dayOfWeek"
                          v-model="form.dayOfWeek"
                          required
                          class="form-input"
                          :class="{ 'border-red-300': errors.dayOfWeek }"
                        >
                          <option value="">Select day</option>
                          <option v-for="(day, index) in daysOfWeek" :key="index" :value="index">
                            {{ day }}
                          </option>
                        </select>
                        <p v-if="errors.dayOfWeek" class="mt-1 text-sm text-red-600">
                          {{ errors.dayOfWeek }}
                        </p>
                      </div>

                      <!-- Time Range -->
                      <div class="grid grid-cols-2 gap-4">
                        <div>
                          <label class="form-label" for="startTime">
                            Start Time *
                          </label>
                          <input
                            id="startTime"
                            v-model="form.startTime"
                            type="time"
                            required
                            class="form-input"
                            :class="{ 'border-red-300': errors.startTime }"
                          />
                          <p v-if="errors.startTime" class="mt-1 text-sm text-red-600">
                            {{ errors.startTime }}
                          </p>
                        </div>
                        <div>
                          <label class="form-label" for="endTime">
                            End Time *
                          </label>
                          <input
                            id="endTime"
                            v-model="form.endTime"
                            type="time"
                            required
                            class="form-input"
                            :class="{ 'border-red-300': errors.endTime }"
                          />
                          <p v-if="errors.endTime" class="mt-1 text-sm text-red-600">
                            {{ errors.endTime }}
                          </p>
                        </div>
                      </div>

                      <!-- Coach -->
                      <div>
                        <label class="form-label" for="coach">
                          Coach *
                        </label>
                        <input
                          id="coach"
                          v-model="form.coach"
                          type="text"
                          required
                          class="form-input"
                          :class="{ 'border-red-300': errors.coach }"
                          placeholder="Coach name"
                        />
                        <p v-if="errors.coach" class="mt-1 text-sm text-red-600">
                          {{ errors.coach }}
                        </p>
                      </div>

                      <!-- Max Class Size -->
                      <div>
                        <label class="form-label" for="maxSize">
                          Maximum Class Size *
                        </label>
                        <input
                          id="maxSize"
                          v-model="form.maxSize"
                          type="number"
                          min="1"
                          max="100"
                          required
                          class="form-input"
                          :class="{ 'border-red-300': errors.maxSize }"
                          placeholder="e.g., 20"
                        />
                        <p v-if="errors.maxSize" class="mt-1 text-sm text-red-600">
                          {{ errors.maxSize }}
                        </p>
                      </div>

                      <!-- Start Date -->
                      <div>
                        <label class="form-label" for="startDate">
                          Class Start Date *
                        </label>
                        <input
                          id="startDate"
                          v-model="form.startDate"
                          type="date"
                          required
                          class="form-input"
                          :class="{ 'border-red-300': errors.startDate }"
                        />
                        <p v-if="errors.startDate" class="mt-1 text-sm text-red-600">
                          {{ errors.startDate }}
                        </p>
                      </div>

                      <!-- Active Status (only for editing) -->
                      <div v-if="isEditing" class="flex items-center">
                        <input
                          id="isActive"
                          v-model="form.isActive"
                          type="checkbox"
                          class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                        />
                        <label for="isActive" class="ml-2 block text-sm text-gray-900">
                          Active Class
                        </label>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="mt-6 flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-3 space-y-3 space-y-reverse sm:space-y-0">
                  <button
                    type="button"
                    class="btn-secondary w-full sm:w-auto"
                    @click="$emit('close')"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    :disabled="loading"
                    class="btn-primary w-full sm:w-auto"
                  >
                    <div v-if="loading" class="spinner mr-2"></div>
                    {{ isEditing ? 'Update Class' : 'Add Class' }}
                  </button>
                </div>
              </form>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue'

// Props
const props = defineProps({
  open: {
    type: Boolean,
    default: false
  },
  classItem: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['close', 'save'])

// Component state
const loading = ref(false)
const errors = ref({})

// Days of the week
const daysOfWeek = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']

// Form data
const form = ref({
  name: '',
  type: '',
  dayOfWeek: '',
  startTime: '',
  endTime: '',
  coach: '',
  maxSize: '',
  startDate: '',
  isActive: true
})

// Computed properties
const isEditing = computed(() => !!props.classItem)

// Watch for class changes to populate form
watch(() => props.classItem, (newClass) => {
  if (newClass) {
    form.value = {
      name: newClass.name || '',
      type: newClass.type || '',
      dayOfWeek: newClass.dayOfWeek !== undefined ? newClass.dayOfWeek : '',
      startTime: newClass.startTime || '',
      endTime: newClass.endTime || '',
      coach: newClass.coach || '',
      maxSize: newClass.maxSize || '',
      startDate: newClass.startDate ? new Date(newClass.startDate).toISOString().split('T')[0] : '',
      isActive: newClass.isActive !== undefined ? newClass.isActive : true
    }
  } else {
    // Reset form for new class
    const today = new Date().toISOString().split('T')[0]
    form.value = {
      name: '',
      type: '',
      dayOfWeek: '',
      startTime: '',
      endTime: '',
      coach: '',
      maxSize: '',
      startDate: today,
      isActive: true
    }
  }
  // Clear errors when class changes
  errors.value = {}
}, { immediate: true })

// Form validation
const validateForm = () => {
  errors.value = {}

  if (!form.value.name.trim()) {
    errors.value.name = 'Class name is required'
  }

  if (!form.value.type) {
    errors.value.type = 'Class type is required'
  }

  if (form.value.dayOfWeek === '') {
    errors.value.dayOfWeek = 'Day of week is required'
  }

  if (!form.value.startTime) {
    errors.value.startTime = 'Start time is required'
  }

  if (!form.value.endTime) {
    errors.value.endTime = 'End time is required'
  }

  if (form.value.startTime && form.value.endTime) {
    if (form.value.startTime >= form.value.endTime) {
      errors.value.endTime = 'End time must be after start time'
    }
  }

  if (!form.value.coach.trim()) {
    errors.value.coach = 'Coach name is required'
  }

  if (!form.value.maxSize || form.value.maxSize < 1) {
    errors.value.maxSize = 'Maximum class size must be at least 1'
  }

  if (!form.value.startDate) {
    errors.value.startDate = 'Start date is required'
  }

  return Object.keys(errors.value).length === 0
}

// Form submission
const handleSubmit = async () => {
  if (!validateForm()) {
    return
  }

  loading.value = true
  try {
    const classData = {
      name: form.value.name.trim(),
      type: form.value.type,
      dayOfWeek: parseInt(form.value.dayOfWeek),
      startTime: form.value.startTime,
      endTime: form.value.endTime,
      coach: form.value.coach.trim(),
      maxSize: parseInt(form.value.maxSize),
      startDate: new Date(form.value.startDate).toISOString()
    }

    // Include isActive only for editing
    if (isEditing.value) {
      classData.isActive = form.value.isActive
    }

    emit('save', classData)
  } catch (error) {
    console.error('Form submission error:', error)
  } finally {
    loading.value = false
  }
}
</script>