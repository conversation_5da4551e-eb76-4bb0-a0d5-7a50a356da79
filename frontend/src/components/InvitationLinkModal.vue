<template>
  <TransitionRoot appear :show="open" as="template">
    <Dialog as="div" @close="$emit('close')" class="relative z-10">
      <TransitionChild
        as="template"
        enter="duration-300 ease-out"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="duration-200 ease-in"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div class="fixed inset-0 bg-black bg-opacity-25" />
      </TransitionChild>

      <div class="fixed inset-0 overflow-y-auto">
        <div class="flex min-h-full items-center justify-center p-4 text-center">
          <TransitionChild
            as="template"
            enter="duration-300 ease-out"
            enter-from="opacity-0 scale-95"
            enter-to="opacity-100 scale-100"
            leave="duration-200 ease-in"
            leave-from="opacity-100 scale-100"
            leave-to="opacity-0 scale-95"
          >
            <DialogPanel class="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
              <DialogTitle as="h3" class="text-lg font-medium leading-6 text-gray-900">
                Member Invitation
              </DialogTitle>
              
              <div class="mt-4">
                <p class="text-sm text-gray-500 mb-2">
                  An invitation has been created. You can share this link with the member:
                </p>
                <div class="flex items-center mt-2">
                  <input 
                    type="text" 
                    readonly 
                    :value="inviteLink" 
                    class="flex-grow p-2 border rounded-l text-sm bg-gray-50"
                    ref="linkInput"
                  />
                  <button 
                    @click="copyLink" 
                    class="bg-blue-600 text-white px-3 py-2 rounded-r hover:bg-blue-700"
                  >
                    Copy
                  </button>
                </div>
                <p v-if="copied" class="text-green-600 text-sm mt-1">
                  Link copied to clipboard!
                </p>
              </div>

              <div class="mt-6 flex justify-end">
                <button
                  type="button"
                  class="inline-flex justify-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-900 hover:bg-blue-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2"
                  @click="$emit('close')"
                >
                  Close
                </button>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup>
import { ref } from 'vue'
import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue'

const props = defineProps({
  open: {
    type: Boolean,
    default: false
  },
  inviteLink: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['close'])

const copied = ref(false)
const linkInput = ref(null)

const copyLink = () => {
  if (linkInput.value) {
    linkInput.value.select()
    document.execCommand('copy')
    copied.value = true
    
    // Reset the copied state after 3 seconds
    setTimeout(() => {
      copied.value = false
    }, 3000)
  }
}
</script>
