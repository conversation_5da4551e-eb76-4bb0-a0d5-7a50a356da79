import { defineStore } from 'pinia'
import { ref } from 'vue'
import { apiClient } from '@/services/api'

export const useMembershipsStore = defineStore('memberships', () => {
  // State
  const plans = ref([])
  const loading = ref(false)
  const error = ref(null)

  // Actions
  const fetchMembershipPlans = async () => {
    try {
      loading.value = true
      error.value = null
      const response = await apiClient.getMembershipPlans()
      plans.value = response.plans || []
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  const createMembershipPlan = async (planData) => {
    try {
      error.value = null
      const response = await apiClient.createMembershipPlan(planData)
      plans.value.push(response.plan)
      return response.plan
    } catch (err) {
      error.value = err.message
      throw err
    }
  }

  const updateMembershipPlan = async (id, planData) => {
    try {
      error.value = null
      await apiClient.updateMembershipPlan(id, planData)
      
      // Update the plan in the local state
      const index = plans.value.findIndex(p => p.id === id)
      if (index !== -1) {
        plans.value[index] = { ...plans.value[index], ...planData }
      }
      
      return { success: true }
    } catch (err) {
      error.value = err.message
      throw err
    }
  }

  const deleteMembershipPlan = async (id) => {
    try {
      error.value = null
      await apiClient.deleteMembershipPlan(id)
      
      // Remove the plan from the local state
      plans.value = plans.value.filter(p => p.id !== id)
      
      return { success: true }
    } catch (err) {
      error.value = err.message
      throw err
    }
  }

  const togglePlanStatus = async (plan) => {
    try {
      const newStatus = !plan.isActive
      await updateMembershipPlan(plan.id, { isActive: newStatus })
      return { success: true }
    } catch (err) {
      error.value = err.message
      throw err
    }
  }

  const clearError = () => {
    error.value = null
  }

  return {
    // State
    plans,
    loading,
    error,
    // Actions
    fetchMembershipPlans,
    createMembershipPlan,
    updateMembershipPlan,
    deleteMembershipPlan,
    togglePlanStatus,
    clearError
  }
})
