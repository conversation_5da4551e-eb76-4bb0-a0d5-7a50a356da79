// src/stores/dashboard.js
import { defineStore } from 'pinia'
import { ref } from 'vue'
import { apiClient } from '@/services/api'

export const useDashboardStore = defineStore('dashboard', () => {
  // State
  const stats = ref({
    totalMembers: 0,
    activeMembers: 0,
    totalClasses: 0,
    activeClasses: 0
  })
  const memberGrowth = ref({
    newMembersThisMonth: 0,
    newMembersLastMonth: 0,
    monthlyGrowth: []
  })
  const classUtilization = ref({
    classes: [],
    overallUtilization: 0,
    totalCapacity: 0,
    totalUtilized: 0
  })
  const loading = ref(false)
  const error = ref(null)

  // Actions
  const fetchDashboardStats = async () => {
    try {
      loading.value = true
      error.value = null
      const response = await apiClient.getDashboardStats()
      stats.value = response.stats
    } catch (err) {
      error.value = err.message
      console.error('fetchDashboardStats error:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchAllInsights = async () => {
    try {
      loading.value = true
      error.value = null
      const response = await apiClient.getAllInsights()
      const insights = response.insights
      
      // Update all state from single response
      stats.value = insights.basicStats
      memberGrowth.value = insights.memberGrowth
      classUtilization.value = insights.classUtilization
    } catch (err) {
      error.value = err.message
      console.error('fetchAllInsights error:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchMemberGrowthStats = async () => {
    try {
      error.value = null
      const response = await apiClient.getMemberGrowthStats()
      memberGrowth.value = response.memberGrowth
    } catch (err) {
      error.value = err.message
      console.error('fetchMemberGrowthStats error:', err)
      throw err
    }
  }

  const clearError = () => {
    error.value = null
  }

  return {
    // State
    stats,
    memberGrowth,
    classUtilization,
    loading,
    error,
    // Actions
    fetchDashboardStats,
    fetchAllInsights,
    fetchMemberGrowthStats,
    clearError
  }
})
