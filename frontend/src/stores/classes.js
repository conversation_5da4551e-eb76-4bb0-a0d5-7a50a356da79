// src/stores/classes.js
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { apiClient } from '@/services/api'

export const useClassesStore = defineStore('classes', () => {
  // State
  const templates = ref([])
  const instances = ref([])
  const loading = ref(false)
  const error = ref(null)

  // Computed properties
  const activeTemplates = computed(() => templates.value.filter(t => t.isActive))
  const upcomingInstances = computed(() => instances.value.filter(i => 
    new Date(i.date) >= new Date() && i.status !== 'cancelled'))

  // Actions
  const fetchClassSchedule = async (weeksAhead = 4) => {
    try {
      loading.value = true
      error.value = null
      const response = await apiClient.getClasses()
      templates.value = response.classes || []
      
      // Fetch instances if available
      try {
        const instancesResponse = await apiClient.getClassInstances(weeksAhead)
        instances.value = instancesResponse.instances || []
      } catch (err) {
        console.error('Failed to fetch class instances:', err)
        instances.value = []
      }
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  const createClassTemplate = async (classData) => {
    try {
      error.value = null
      console.log('Creating class template with data:', classData)
      const response = await apiClient.createClass(classData)
      console.log('Class template created:', response)
      templates.value.push(response.class)
      return response.class
    } catch (err) {
      console.error('Failed to create class template:', err)
      error.value = err.message || 'Failed to create class template'
      throw err
    }
  }

  const deleteClassTemplate = async (id) => {
    try {
      error.value = null
      await apiClient.deleteClass(id)
      
      // Remove from local state
      templates.value = templates.value.filter(c => c.id !== id)
    } catch (err) {
      error.value = err.message
      throw err
    }
  }

  const generateInstances = async (weeksAhead = 8) => {
    try {
      error.value = null
      loading.value = true
      await apiClient.generateClassInstances(weeksAhead)
      // Refresh instances after generation
      const response = await apiClient.getClassInstances(weeksAhead)
      instances.value = response.instances || []
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  const clearError = () => {
    error.value = null
  }

  const updateClassTemplate = async (id, classData) => {
    try {
      error.value = null
      console.log('Updating class template with data:', classData)
      const response = await apiClient.updateClass(id, classData)
      console.log('Class template updated:', response)
      
      // Update the template in the local state
      const index = templates.value.findIndex(t => t.id === id)
      if (index !== -1) {
        templates.value[index] = { ...templates.value[index], ...classData }
      }
      
      return response
    } catch (err) {
      console.error('Failed to update class template:', err)
      error.value = err.message || 'Failed to update class template'
      throw err
    }
  }

  const cancelClassInstance = async (id, reason) => {
    try {
      error.value = null
      loading.value = true
      await apiClient.cancelClassInstance(id, reason)
      
      // Update the instance in the local state
      const index = instances.value.findIndex(i => i.id === id)
      if (index !== -1) {
        instances.value[index] = { 
          ...instances.value[index], 
          status: 'cancelled',
          cancellationReason: reason
        }
      }
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  return {
    // State
    templates,
    instances,
    loading,
    error,
    // Computed
    activeTemplates,
    upcomingInstances,
    // Actions
    fetchClassSchedule,
    createClassTemplate,
    updateClassTemplate,
    deleteClassTemplate,
    generateInstances,
    cancelClassInstance,
    clearError
  }
})
