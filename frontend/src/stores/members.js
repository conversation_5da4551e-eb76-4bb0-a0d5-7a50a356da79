// src/stores/members.js

import { defineStore } from 'pinia'
import { ref } from 'vue'
import { apiClient } from '@/services/api'

export const useMembersStore = defineStore('members', () => {
  // State
  const members = ref([])
  const loading = ref(false)
  const error = ref(null)

  // Actions
  const fetchMembers = async () => {
    try {
      loading.value = true
      error.value = null
      console.log('Fetching members...')
      const response = await apiClient.getMembers()
      console.log('Members response:', response)
      members.value = response.members || []
    } catch (err) {
      console.error('Error fetching members:', err)
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  const createMember = async (memberData) => {
    try {
      error.value = null
      const response = await apiClient.createMember(memberData)
      members.value.push(response.member)
      return response.member
    } catch (err) {
      error.value = err.message
      throw err
    }
  }

  const updateMember = async (id, memberData) => {
    try {
      error.value = null
      await apiClient.updateMember(id, memberData)
      
      // Update local state
      const index = members.value.findIndex(m => m.id === id)
      if (index !== -1) {
        members.value[index] = { ...members.value[index], ...memberData }
      }
    } catch (err) {
      error.value = err.message
      throw err
    }
  }

  const deleteMember = async (id) => {
    try {
      error.value = null
      await apiClient.deleteMember(id)
      
      // Remove from local state
      members.value = members.value.filter(m => m.id !== id)
    } catch (err) {
      error.value = err.message
      throw err
    }
  }

  const clearError = () => {
    error.value = null
  }

  const sendInvitation = async (memberId) => {
    loading.value = true
    error.value = null
    try {
      const response = await apiClient.sendMemberInvitation(memberId)
      return response
    } catch (err) {
      console.error('Failed to send invitation:', err)
      error.value = err.message || 'Failed to send invitation'
      throw err
    } finally {
      loading.value = false
    }
  }

  return {
    members,
    loading,
    error,
    fetchMembers,
    createMember,
    updateMember,
    deleteMember,
    sendInvitation,
    clearError
  }
})
