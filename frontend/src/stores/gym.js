// src/stores/gym.js
import { defineStore } from 'pinia'
import { ref } from 'vue'
import { apiClient } from '@/services/api'

export const useGymStore = defineStore('gym', () => {
  // State
  const gym = ref({
    id: '',
    name: '',
    address: '',
    phone: '',
    email: '',
    ownerUid: '',
    owners: [],
    createdAt: '',
    updatedAt: '',
    isActive: true
  })
  const loading = ref(false)
  const error = ref(null)

  // Actions
  const fetchGymInfo = async () => {
    try {
      loading.value = true
      error.value = null
      const response = await apiClient.getGymInfo()
      gym.value = response.gym
      return response.gym
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateGymInfo = async (gymData) => {
    try {
      error.value = null
      await apiClient.updateGymInfo(gymData)
      
      // Update local state with new data
      gym.value = { ...gym.value, ...gymData, updatedAt: new Date().toISOString() }
      
      return gym.value
    } catch (err) {
      error.value = err.message
      throw err
    }
  }

  const clearError = () => {
    error.value = null
  }

  return {
    // State
    gym,
    loading,
    error,
    // Actions
    fetchGymInfo,
    updateGymInfo,
    clearError
  }
})