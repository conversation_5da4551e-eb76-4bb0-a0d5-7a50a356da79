// src/stores/auth.js
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { 
  auth, 
  signInWithGoogle, 
  signInWithEmail,
  registerWithEmail,
  resetPassword,
  logOut 
} from '@/services/firebase'
import { apiClient } from '@/services/api'
import { onAuthStateChanged } from 'firebase/auth'
import { useRouter } from 'vue-router'

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref(null)
  const loading = ref(true)
  const error = ref(null)
  const router = useRouter()

  // Getters
  const isAuthenticated = computed(() => !!user.value)
  const userEmail = computed(() => user.value?.email || '')
  const userName = computed(() => user.value?.displayName || '')
  const isGymOwner = computed(() => user.value?.role === 'owner')
  const isMember = computed(() => user.value?.role === 'member')

  // Actions
  const initializeAuth = () => {
    return new Promise((resolve) => {
      onAuthStateChanged(auth, async (firebaseUser) => {
        if (firebaseUser) {
          try {
            // Verify token with backend
            const idToken = await firebaseUser.getIdToken()
            const response = await apiClient.verifyToken(idToken)
            
            user.value = {
              uid: firebaseUser.uid,
              email: firebaseUser.email,
              displayName: firebaseUser.displayName,
              photoURL: firebaseUser.photoURL,
              role: response.user?.role || 'owner', // Default to owner for backward compatibility
              gymID: response.user?.gymID || response.user?.GymID || '',
              ...response.user
            }
            
            // Redirect members to member dashboard if they try to access owner pages
            if (user.value.role === 'member' && router.currentRoute.value.meta.requiresOwner) {
              router.push('/member-dashboard')
            }
          } catch (err) {
            console.error('Failed to verify user with backend:', err)
            error.value = err.message
            user.value = null
          }
        } else {
          user.value = null
        }
        loading.value = false
        resolve()
      })
    })
  }

  const signIn = async () => {
    try {
      loading.value = true
      error.value = null
      
      const result = await signInWithGoogle()
      const idToken = await result.user.getIdToken()
      
      // Verify with backend
      const response = await apiClient.verifyToken(idToken)
      
      user.value = {
        uid: result.user.uid,
        email: result.user.email,
        displayName: result.user.displayName,
        photoURL: result.user.photoURL,
        role: response.user?.role || 'owner', // Default to owner for backward compatibility
        gymID: response.user?.gymID || response.user?.GymID || '',
        ...response.user
      }
      
      // Redirect members to member dashboard
      if (user.value.role === 'member') {
        router.push('/member-dashboard')
      }
      
      return user.value
    } catch (err) {
      console.error('Sign in error:', err)
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  const signInWithEmailPassword = async (email, password) => {
    try {
      loading.value = true
      error.value = null
      
      const result = await signInWithEmail(email, password)
      const idToken = await result.user.getIdToken()
      
      // Verify with backend
      const response = await apiClient.verifyToken(idToken)
      
      user.value = {
        uid: result.user.uid,
        email: result.user.email,
        displayName: result.user.displayName,
        photoURL: result.user.photoURL,
        role: response.user?.role || 'owner', // Default to owner for backward compatibility
        gymID: response.user?.gymID || response.user?.GymID || '',
        ...response.user
      }
      
      // Redirect members to member dashboard
      if (user.value.role === 'member') {
        router.push('/member-dashboard')
      }
      
      return user.value
    } catch (err) {
      console.error('Email sign in error:', err)
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  const register = async (email, password, displayName) => {
    try {
      loading.value = true
      error.value = null
      
      const result = await registerWithEmail(email, password, displayName)
      const idToken = await result.user.getIdToken()
      
      // Verify with backend
      const response = await apiClient.verifyToken(idToken)
      
      user.value = {
        uid: result.user.uid,
        email: result.user.email,
        displayName: result.user.displayName,
        photoURL: result.user.photoURL,
        gymID: response.user?.gymID || response.user?.GymID || '',
        ...response.user
      }
      
      return user.value
    } catch (err) {
      console.error('Registration error:', err)
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  const forgotPassword = async (email) => {
    try {
      loading.value = true
      error.value = null
      
      await resetPassword(email)
      return true
    } catch (err) {
      console.error('Password reset error:', err)
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  const signOut = async () => {
    try {
      await logOut()
      user.value = null
      error.value = null
    } catch (err) {
      error.value = err.message
      throw err
    }
  }

  const clearError = () => {
    error.value = null
  }

  const registerMember = async (email, password, memberId, accessCode) => {
    try {
      loading.value = true
      error.value = null
      
      // Register with Firebase
      const result = await registerWithEmail(email, password, email.split('@')[0])
      
      // Link the member account with the member ID
      await apiClient.linkMemberAccount(memberId, accessCode, result.user.uid)
      
      return result
    } catch (err) {
      console.error('Member registration error:', err)
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  return {
    // State
    user,
    loading,
    error,
    // Getters
    isAuthenticated,
    userEmail,
    userName,
    isGymOwner,
    isMember,
    // Actions
    initializeAuth,
    signIn,
    signInWithEmailPassword,
    register,
    forgotPassword,
    signOut,
    clearError,
    registerMember,
  }
})
