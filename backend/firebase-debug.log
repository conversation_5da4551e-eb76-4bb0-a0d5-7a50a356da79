[debug] [2025-07-13T12:20:44.582Z] ----------------------------------------------------------------------
[debug] [2025-07-13T12:20:44.591Z] Command:       /usr/local/bin/firebase /Users/<USER>/.cache/firebase/tools/lib/node_modules/firebase-tools/lib/bin/firebase emulators:start --only firestore,auth --project demo-project --import=emulator-data --export-on-exit=emulator-data --config=../firebase/firebase.json
[debug] [2025-07-13T12:20:44.591Z] CLI Version:   14.4.0
[debug] [2025-07-13T12:20:44.591Z] Platform:      darwin
[debug] [2025-07-13T12:20:44.591Z] Node Version:  v20.18.2
[debug] [2025-07-13T12:20:44.592Z] Time:          Sun Jul 13 2025 13:20:44 GMT+0100 (British Summer Time)
[debug] [2025-07-13T12:20:44.592Z] Env Overrides: FIRESTORE_EMULATOR_HOST
[debug] [2025-07-13T12:20:44.593Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-07-13T12:20:44.597Z] >>> [apiv2][query] GET https://firebase-public.firebaseio.com/cli.json [none]
[debug] [2025-07-13T12:20:45.338Z] <<< [apiv2][status] GET https://firebase-public.firebaseio.com/cli.json 200
[debug] [2025-07-13T12:20:45.339Z] <<< [apiv2][body] GET https://firebase-public.firebaseio.com/cli.json {"cloudBuildErrorAfter":1594252800000,"cloudBuildWarnAfter":1590019200000,"defaultNode10After":1594252800000,"minVersion":"3.0.5","node8DeploysDisabledAfter":1613390400000,"node8RuntimeDisabledAfter":1615809600000,"node8WarnAfter":1600128000000}
