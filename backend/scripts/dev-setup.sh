#!/bin/bash

# Development setup script for DojoFy backend
# This script starts the Firebase emulators and seeds them with test data

set -e

echo "🚀 Setting up DojoFy development environment..."

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    echo "❌ Firebase CLI is not installed. Please install it first:"
    echo "   npm install -g firebase-tools"
    exit 1
fi

# Check if Go is installed
if ! command -v go &> /dev/null; then
    echo "❌ Go is not installed. Please install it first."
    exit 1
fi

echo "✅ Prerequisites check passed"

# Start emulators in the background
echo "🔥 Starting Firebase emulators..."
make emulators &
EMULATOR_PID=$!

# Wait for emulators to start
echo "⏳ Waiting for emulators to start..."
sleep 10

# Check if emulators are running
if ! curl -s http://localhost:8081 > /dev/null; then
    echo "❌ Firestore emulator is not responding. Please check the emulator logs."
    kill $EMULATOR_PID 2>/dev/null || true
    exit 1
fi

echo "✅ Emulators are running"

# Seed the database
echo "🌱 Seeding database with test data..."
make seed

echo "✅ Development environment is ready!"
echo ""
echo "📋 Available commands:"
echo "   make run     - Start the backend server"
echo "   make stop    - Stop the emulators"
echo "   make clean   - Clear emulator data"
echo ""
echo "🌐 Emulator URLs:"
echo "   Firestore: http://localhost:8081"
echo "   Auth:      http://localhost:9099"
echo ""
echo "Press Ctrl+C to stop the emulators"

# Wait for user to stop
wait $EMULATOR_PID 