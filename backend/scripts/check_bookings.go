package main

import (
	"context"
	"fmt"
	"log"

	"cloud.google.com/go/firestore"
	"google.golang.org/api/option"
)

func main() {
	ctx := context.Background()

	// Connect to Firestore emulator
	client, err := firestore.NewClient(ctx, "demo-project",
		option.WithEndpoint("localhost:8080"),
		option.WithoutAuthentication(),
	)
	if err != nil {
		log.Fatalf("Failed to create client: %v", err)
	}
	defer client.Close()

	// Check existing bookings
	fmt.Println("=== Checking existing bookings ===")
	bookings := client.Collection("bookings")
	docs, err := bookings.Documents(ctx).GetAll()
	if err != nil {
		log.Fatalf("Failed to get bookings: %v", err)
	}

	fmt.Printf("Found %d existing bookings:\n", len(docs))
	for _, doc := range docs {
		fmt.Printf("  - ID: %s, Data: %+v\n", doc.Ref.ID, doc.Data())
	}

	// Check users
	fmt.Println("\n=== Checking users ===")
	users := client.Collection("users")
	userDocs, err := users.Documents(ctx).GetAll()
	if err != nil {
		log.Fatalf("Failed to get users: %v", err)
	}

	fmt.Printf("Found %d users:\n", len(userDocs))
	for _, doc := range userDocs {
		fmt.Printf("  - ID: %s, Data: %+v\n", doc.Ref.ID, doc.Data())
	}

	// Check class instances
	fmt.Println("\n=== Checking class instances ===")
	instances := client.Collection("class_instances")
	instanceDocs, err := instances.Documents(ctx).GetAll()
	if err != nil {
		log.Fatalf("Failed to get class instances: %v", err)
	}

	fmt.Printf("Found %d class instances:\n", len(instanceDocs))
	for _, doc := range instanceDocs {
		data := doc.Data()
		fmt.Printf("  - ID: %s, Name: %s, Enrolled: %v/%v\n",
			doc.Ref.ID,
			data["name"],
			data["enrolledCount"],
			data["maxCapacity"])
	}

	// If there are no bookings but there's a class with enrolledCount > 0, create a booking
	if len(docs) == 0 && len(userDocs) > 0 && len(instanceDocs) > 0 {
		fmt.Println("\n=== Creating missing booking ===")

		// Find a class with enrolledCount > 0
		var targetInstance *firestore.DocumentSnapshot
		for _, doc := range instanceDocs {
			data := doc.Data()
			if enrolled, ok := data["enrolledCount"].(int64); ok && enrolled > 0 {
				targetInstance = doc
				break
			}
		}

		if targetInstance != nil {
			// Use the first user as the member
			userDoc := userDocs[0]
			userData := userDoc.Data()

			// Create booking
			bookingData := map[string]interface{}{
				"classId":   targetInstance.Ref.ID,
				"memberId":  userDoc.Ref.ID,
				"gymId":     userData["gymId"],
				"startTime": targetInstance.Data()["startTime"],
				"endTime":   targetInstance.Data()["endTime"],
			}

			_, _, err := bookings.Add(ctx, bookingData)
			if err != nil {
				log.Fatalf("Failed to create booking: %v", err)
			}

			fmt.Printf("Created booking for class %s (%s) by user %s\n",
				targetInstance.Ref.ID,
				targetInstance.Data()["name"],
				userDoc.Ref.ID)
		}
	}

	fmt.Println("\n=== Done ===")
}
