# Local Development with Firebase Emulators

This backend can be run locally using the Firebase Emulator Suite for Firestore and Auth. No Google credentials are required for local development.

## Prerequisites
- [Node.js](https://nodejs.org/) (for Firebase CLI)
- [Firebase CLI](https://firebase.google.com/docs/cli) (`npm install -g firebase-tools`)
- Go 1.20+

## Setup
1. Copy `.env.example` to `.env` and adjust as needed:
   ```sh
   cp .env.example .env
   ```
2. Install dependencies:
   ```sh
   go mod tidy
   ```
3. Start the Firebase emulators:
   ```sh
   make emulators
   ```
   This will start Firestore and Auth emulators. Leave this running in a terminal.

4. In a new terminal, run the backend:
   ```sh
   make run
   ```
   The backend will connect to the emulators automatically.

5. To clean up emulator data:
   ```sh
   make clean
   ```

## Notes
- The backend will use the emulators if `USE_FIREBASE_EMULATORS=true` is set (see `.env.example`).
- No Google credentials are required when using emulators.
- To stop the emulators, press Ctrl+C in the terminal running `make emulators`. 