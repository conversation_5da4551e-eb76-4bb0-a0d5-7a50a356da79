package config

import (
	"os"
)

// Config holds application configuration
type Config struct {
	Port                         string
	Environment                  string
	FirebaseProjectID            string
	GoogleApplicationCredentials string
	CORSOrigin                   string
	ResendAPIKey                 string
	EmailFrom                    string
	StripeSecretKey              string
	StripePublishableKey         string
	StripeWebhookSecret          string
	StripeSuccessURL             string
	StripeCancelURL              string
}

// getEnv gets an environment variable or returns a default value
func getEnv(key, defaultValue string) string {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}
	return value
}

// Load reads configuration from environment variables and returns a Config struct
func Load() *Config {
	corsOrigin := getEnv("CORS_ORIGIN", "http://localhost:5173")

	// If CORS_ALLOW_ALL is set to true, we'll use * for CORS
	if getEnv("CORS_ALLOW_ALL", "false") == "true" {
		corsOrigin = "*"
	}

	return &Config{
		Port:                         getEnv("PORT", "8080"),
		Environment:                  getEnv("GIN_MODE", "debug"),
		FirebaseProjectID:            getEnv("FIREBASE_PROJECT_ID", ""),
		GoogleApplicationCredentials: getEnv("GOOGLE_APPLICATION_CREDENTIALS", ""),
		CORSOrigin:                   corsOrigin,
		ResendAPIKey:                 getEnv("RESEND_API_KEY", ""),
		EmailFrom:                    getEnv("EMAIL_FROM", "<EMAIL>"),
		StripeSecretKey:              getEnv("STRIPE_SECRET_KEY", ""),
		StripePublishableKey:         getEnv("STRIPE_PUBLISHABLE_KEY", ""),
		StripeWebhookSecret:          getEnv("STRIPE_WEBHOOK_SECRET", ""),
		StripeSuccessURL:             getEnv("STRIPE_SUCCESS_URL", "http://localhost:5173/payment/success"),
		StripeCancelURL:              getEnv("STRIPE_CANCEL_URL", "http://localhost:5173/payment/cancel"),
	}
}
