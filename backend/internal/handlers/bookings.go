package handlers

import (
	"context"
	"dojofy/internal/models"
	"dojofy/internal/services"
	"fmt"
	"log"
	"net/http"
	"time"

	"cloud.google.com/go/firestore"
	"github.com/gin-gonic/gin"
	"google.golang.org/api/iterator"
)

// Custom error type for booking validation
type BookingError struct {
	Message string
}

func (e *BookingError) Error() string {
	return e.Message
}

type BookingHandler struct {
	firebaseService *services.FirebaseService
}

func NewBookingHandler(firebaseService *services.FirebaseService) *BookingHandler {
	return &BookingHandler{
		firebaseService: firebaseService,
	}
}

func (h *BookingHandler) GetAllBookingsForUser(c *gin.Context) {
	log.Printf("GetAllBookingsForUser called")
	memberID, exists := c.Get("memberID")
	if !exists || memberID == "" {
		log.Printf("No memberID found in context")
		c.<PERSON>(http.StatusForbidden, gin.H{"error": "No user access"})
		return
	}
	log.Printf("Getting bookings for memberID: %s", memberID)

	iter := h.firebaseService.Collection("bookings").Where("memberId", "==", memberID).Documents(c.Request.Context())

	var bookings []models.Booking
	for {
		doc, err := iter.Next()
		if err == iterator.Done {
			break
		}
		if err != nil {
			log.Printf("Error fetching booking: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch bookings"})
			return
		}
		var booking models.Booking
		if err := doc.DataTo(&booking); err != nil {
			log.Printf("Error parsing booking data: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to parse booking data"})
			return
		}
		booking.ID = doc.Ref.ID
		bookings = append(bookings, booking)
	}

	log.Printf("Found %d bookings for member %s", len(bookings), memberID)
	log.Printf("Returning bookings: %+v", bookings)

	// Always return a JSON array, even if empty
	if bookings == nil {
		bookings = []models.Booking{}
	}

	c.JSON(http.StatusOK, bookings)
}

func (h *BookingHandler) GetAllBookingsForGym(c *gin.Context) {
	memberID, exists := c.Get("memberID")
	if !exists || memberID == "" {
		c.JSON(http.StatusForbidden, gin.H{"error": "No user access"})
		return
	}

	iter := h.firebaseService.Collection("bookings").Where("gymId", "==", memberID).Documents(c.Request.Context())

	var bookings []models.Booking
	for {
		doc, err := iter.Next()
		if err == iterator.Done {
			break
		}
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch bookings"})
			return
		}
		var booking models.Booking
		if err := doc.DataTo(&booking); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to parse booking data"})
			return
		}
		booking.ID = doc.Ref.ID
		bookings = append(bookings, booking)
	}

	c.JSON(http.StatusOK, bookings)
}

func (h *BookingHandler) GetAllBookingsForClass(c *gin.Context) {
	classID := c.Param("id")

	iter := h.firebaseService.Collection("bookings").Where("classId", "==", classID).Documents(c.Request.Context())

	var bookings []models.Booking
	for {
		doc, err := iter.Next()
		if err == iterator.Done {
			break
		}
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch bookings"})
			return
		}
		var booking models.Booking
		if err := doc.DataTo(&booking); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to parse booking data"})
			return
		}
		booking.ID = doc.Ref.ID
		bookings = append(bookings, booking)
	}

	c.JSON(http.StatusOK, bookings)
}

func (h *BookingHandler) GetBookingByID(c *gin.Context) {
	bookingID := c.Param("id")

	doc, err := h.firebaseService.GetDocument(c.Request.Context(), "bookings", bookingID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Booking not found"})
		return
	}

	var booking models.Booking
	if err := doc.DataTo(&booking); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to parse booking data"})
		return
	}

	booking.ID = doc.Ref.ID
	c.JSON(http.StatusOK, gin.H{"booking": booking})
}

func (h *BookingHandler) CreateBooking(c *gin.Context) {
	memberID, exists := c.Get("memberID")
	if !exists || memberID == "" {
		c.JSON(http.StatusForbidden, gin.H{"error": "No user access"})
		return
	}

	memberIDStr := memberID.(string)
	errParams := map[string]string{
		"memberID": memberIDStr,
	}

	var request models.CreateBookingRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	errParams["classID"] = request.ClassID

	var booking models.Booking

	// Use a transaction to ensure atomicity and prevent race conditions
	err := h.firebaseService.RunTransaction(c.Request.Context(), func(ctx context.Context, tx *firestore.Transaction) error {
		// 1. Get the class and check if it's full
		classDoc, err := tx.Get(h.firebaseService.Collection("class_instances").Doc(request.ClassID))
		if err != nil {
			return fmt.Errorf("failed to get class document: %w", err, errParams)
		}

		var class models.ClassInstance
		if err := classDoc.DataTo(&class); err != nil {
			return fmt.Errorf("failed to parse class data: %w", err, errParams)
		}

		// Check if class is full
		if class.EnrolledCount >= class.MaxCapacity {
			return &BookingError{Message: "Class is full"}
		}

		// 2. Get the member and their membership plan
		memberDoc, err := tx.Get(h.firebaseService.Collection("members").Doc(memberIDStr))
		if err != nil {
			return fmt.Errorf("failed to get member document: %w", err, errParams)
		}

		var member models.Member
		if err := memberDoc.DataTo(&member); err != nil {
			return fmt.Errorf("failed to parse member data: %w", err, errParams)
		}

		errParams["membershipPlanID"] = member.MembershipPlanID

		// Get the membership plan
		membershipDoc, err := tx.Get(h.firebaseService.Collection("membership_plans").Doc(member.MembershipPlanID))
		if err != nil {
			return fmt.Errorf("failed to get membership plan document: %w", err, errParams)
		}

		var membership models.MembershipPlan
		if err := membershipDoc.DataTo(&membership); err != nil {
			return fmt.Errorf("failed to parse membership plan data: %w", err, errParams)
		}

		// 3. Check if member has exceeded their weekly class limit (if not unlimited)
		if membership.ClassesPerWeek > 0 {
			// Calculate the start of the current week (Monday)
			now := time.Now()
			weekStart := now.AddDate(0, 0, -int(now.Weekday()-1))
			weekStart = time.Date(weekStart.Year(), weekStart.Month(), weekStart.Day(), 0, 0, 0, 0, weekStart.Location())
			weekEnd := weekStart.AddDate(0, 0, 7)

			// Count existing bookings for this member in the current week
			bookingsQuery := h.firebaseService.Collection("bookings").
				Where("memberId", "==", memberIDStr).
				Where("startTime", ">=", weekStart).
				Where("startTime", "<", weekEnd)

			bookingsIter := tx.Documents(bookingsQuery)

			weeklyBookingCount := 0
			for {
				_, err := bookingsIter.Next()
				if err == iterator.Done {
					break
				}
				if err != nil {
					return fmt.Errorf("failed to iterate through weekly bookings: %w", err, errParams)
				}
				weeklyBookingCount++
			}

			if weeklyBookingCount >= membership.ClassesPerWeek {
				return &BookingError{Message: "Weekly class limit exceeded"}
			}
		}

		// 4. Check if member already has a booking for this class
		existingBookingQuery := h.firebaseService.Collection("bookings").
			Where("memberId", "==", memberIDStr).
			Where("classId", "==", request.ClassID).
			Limit(1)

		existingBookingIter := tx.Documents(existingBookingQuery)

		_, err = existingBookingIter.Next()
		if err != iterator.Done {
			// Member already has a booking for this class
			return &BookingError{Message: "Member already has a booking for this class"}
		}

		booking = models.Booking{
			MemberID:  memberIDStr,
			GymID:     class.GymID,
			ClassID:   request.ClassID,
			StartTime: class.StartTime,
			EndTime:   class.EndTime,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}

		bookingRef := h.firebaseService.Collection("bookings").NewDoc()
		if err := tx.Set(bookingRef, booking); err != nil {
			return err
		}

		// 6. Increment the class current size
		if err := tx.Update(h.firebaseService.Collection("class_instances").Doc(request.ClassID), []firestore.Update{
			{Path: "enrolledCount", Value: firestore.Increment(1)},
			{Path: "updatedAt", Value: time.Now()},
		}); err != nil {
			return fmt.Errorf("failed to increment class size: %w", err, errParams)
		}

		return nil
	})

	if err != nil {
		// Check if it's a custom error from our validation
		if bookingErr, ok := err.(*BookingError); ok {
			c.JSON(http.StatusBadRequest, gin.H{"error": bookingErr.Message})
			return
		}
		fmt.Printf("Failed to create booking: %v\n", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create booking"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{"booking": booking})
}

func (h *BookingHandler) DeleteBooking(c *gin.Context) {
	bookingID := c.Param("id")

	// Use a transaction to ensure atomicity
	err := h.firebaseService.RunTransaction(c.Request.Context(), func(ctx context.Context, tx *firestore.Transaction) error {
		// 1. Get the booking to find the class ID
		bookingDoc, err := tx.Get(h.firebaseService.Collection("bookings").Doc(bookingID))
		if err != nil {
			return err
		}

		var booking models.Booking
		if err := bookingDoc.DataTo(&booking); err != nil {
			return err
		}

		// 2. Delete the booking
		if err := tx.Delete(h.firebaseService.Collection("bookings").Doc(bookingID)); err != nil {
			return err
		}

		// 3. Decrement the class current size
		if err := tx.Update(h.firebaseService.Collection("class_instances").Doc(booking.ClassID), []firestore.Update{
			{Path: "enrolledCount", Value: firestore.Increment(-1)},
			{Path: "updatedAt", Value: time.Now()},
		}); err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete booking"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Booking deleted successfully"})
}
