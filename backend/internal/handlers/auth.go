// handlers/auth.go
package handlers

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"time"

	"dojofy/internal/models"
	"dojofy/internal/services"

	"cloud.google.com/go/firestore"
	"firebase.google.com/go/v4/auth"
	"github.com/gin-gonic/gin"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

// AuthHandler handles authentication-related requests
type AuthHandler struct {
	firebaseService *services.FirebaseService
}

// NewAuthHandler creates a new authentication handler
func NewAuthHandler(firebaseService *services.FirebaseService) *AuthHandler {
	return &AuthHandler{
		firebaseService: firebaseService,
	}
}

// VerifyToken verifies a Firebase ID token and ensures user exists in database
func (h *AuthHandler) VerifyToken(c *gin.Context) {
	var request struct {
		IDToken string `json:"idToken" binding:"required"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Verify the Firebase ID token
	token, err := h.firebaseService.VerifyIDToken(c.Request.Context(), request.IDToken)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid token"})
		return
	}

	// Use a transaction to prevent race conditions
	err = h.firebaseService.Firestore.RunTransaction(c.Request.Context(), func(ctx context.Context, tx *firestore.Transaction) error {
		// Check if user already exists in our database
		userRef := h.firebaseService.Collection("users").Doc(token.UID)
		userDoc, err := tx.Get(userRef)

		var user *models.User

		if err != nil {
			if status.Code(err) != codes.NotFound {
				return err
			}

			// User doesn't exist, check if they're a member by looking for a member with their UID
			memberQuery := h.firebaseService.Collection("members").Where("userUid", "==", token.UID).Limit(1)
			memberDocs, err := memberQuery.Documents(ctx).GetAll()

			if err == nil && len(memberDocs) > 0 {
				// This is a member, get their gym ID
				var member models.Member
				if err := memberDocs[0].DataTo(&member); err != nil {
					return fmt.Errorf("failed to parse member data: %w", err)
				}

				// Safely extract email and name from claims
				email := ""
				if emailClaim, ok := token.Claims["email"]; ok && emailClaim != nil {
					email = emailClaim.(string)
				}

				name := "User"
				if nameClaim, ok := token.Claims["name"]; ok && nameClaim != nil {
					name = nameClaim.(string)
				}

				// Create user with member role
				user = &models.User{
					UID:       token.UID,
					Email:     email,
					Name:      name,
					GymID:     member.GymID,
					Role:      "member", // Set role as member
					CreatedAt: time.Now(),
					UpdatedAt: time.Now(),
				}

				// Set user document in transaction
				err = tx.Set(userRef, user)
				if err != nil {
					return fmt.Errorf("failed to create user: %w", err)
				}
			} else {
				// Not a member, create new user as owner
				log.Printf("User %s not found, creating new user as owner", token.UID)

				// First check if this user already has a gym (to prevent duplicates)
				gymQuery := h.firebaseService.Collection("gyms").Where("ownerUid", "==", token.UID).Limit(1)
				gymDocs, err := gymQuery.Documents(ctx).GetAll()

				var gymID string

				if err == nil && len(gymDocs) > 0 {
					// User already has a gym, use that one
					gymID = gymDocs[0].Ref.ID
					log.Printf("Found existing gym %s for user %s", gymID, token.UID)
				} else {
					// Create a new gym for this user
					// Safely extract email and name from claims
					email := ""
					if emailClaim, ok := token.Claims["email"]; ok && emailClaim != nil {
						email = emailClaim.(string)
					}

					name := "User"
					if nameClaim, ok := token.Claims["name"]; ok && nameClaim != nil {
						name = nameClaim.(string)
					}

					// If name is still empty, use email or a default
					if name == "" {
						if email != "" {
							name = email
						} else {
							name = "New User"
						}
					}

					gym := &models.Gym{
						Name:      name + "'s Gym",
						Address:   "",
						Phone:     "",
						Email:     email,
						OwnerUID:  token.UID,
						Owners:    []string{token.UID},
						CreatedAt: time.Now(),
						UpdatedAt: time.Now(),
						IsActive:  true,
					}

					// Create a new gym document reference
					gymRef := h.firebaseService.Collection("gyms").NewDoc()
					gymID = gymRef.ID

					// Set gym document in transaction
					err = tx.Set(gymRef, gym)
					if err != nil {
						return fmt.Errorf("failed to create gym: %w", err)
					}

					log.Printf("Created new gym %s for user %s", gymID, token.UID)
				}

				// Safely extract email and name from claims
				email := ""
				if emailClaim, ok := token.Claims["email"]; ok && emailClaim != nil {
					email = emailClaim.(string)
				}

				name := "User"
				if nameClaim, ok := token.Claims["name"]; ok && nameClaim != nil {
					name = nameClaim.(string)
				}

				// Create user with the gym ID
				user = &models.User{
					UID:       token.UID,
					Email:     email,
					Name:      name,
					GymID:     gymID,
					Role:      "owner", // Set role as owner
					CreatedAt: time.Now(),
					UpdatedAt: time.Now(),
				}

				// Set user document in transaction
				err = tx.Set(userRef, user)
				if err != nil {
					return fmt.Errorf("failed to create user: %w", err)
				}
			}
		} else {
			// User exists, get user data
			user = &models.User{}
			if err := userDoc.DataTo(user); err != nil {
				return fmt.Errorf("failed to parse user data: %w", err)
			}

			// If user has no gym ID but is an owner, find or create a gym
			if user.GymID == "" && user.Role == "owner" {
				// Check if this user already has a gym (to prevent duplicates)
				gymQuery := h.firebaseService.Collection("gyms").Where("ownerUid", "==", token.UID).Limit(1)
				gymDocs, err := gymQuery.Documents(ctx).GetAll()

				var gymID string

				if err == nil && len(gymDocs) > 0 {
					// User already has a gym, use that one
					gymID = gymDocs[0].Ref.ID
					log.Printf("Found existing gym %s for user %s", gymID, token.UID)
				} else {
					// Create a new gym for this user
					email := user.Email
					if email == "" {
						if emailClaim, ok := token.Claims["email"]; ok && emailClaim != nil {
							email = emailClaim.(string)
						}
					}

					name := user.Name
					if name == "" {
						if nameClaim, ok := token.Claims["name"]; ok && nameClaim != nil {
							name = nameClaim.(string)
						}
					}

					// If name is still empty, use email or a default
					if name == "" {
						if email != "" {
							name = email
						} else {
							name = "New User"
						}
					}

					gym := &models.Gym{
						Name:      name + "'s Gym",
						Address:   "",
						Phone:     "",
						Email:     email,
						OwnerUID:  token.UID,
						Owners:    []string{token.UID},
						CreatedAt: time.Now(),
						UpdatedAt: time.Now(),
						IsActive:  true,
					}

					// Create a new gym document reference
					gymRef := h.firebaseService.Collection("gyms").NewDoc()
					gymID = gymRef.ID

					// Set gym document in transaction
					err = tx.Set(gymRef, gym)
					if err != nil {
						return fmt.Errorf("failed to create gym: %w", err)
					}

					log.Printf("Created new gym %s for user %s", gymID, token.UID)
				}

				// Update user with gym ID
				user.GymID = gymID
				user.UpdatedAt = time.Now()

				// Set user document in transaction
				err = tx.Set(userRef, user)
				if err != nil {
					return fmt.Errorf("failed to update user: %w", err)
				}
			}
		}

		// Store user in context for response
		c.Set("currentUser", user)

		return nil
	})

	if err != nil {
		log.Printf("Transaction failed: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to process user data"})
		return
	}

	// Get user from context
	userValue, exists := c.Get("currentUser")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "User data not found"})
		return
	}

	user := userValue.(*models.User)

	// Run cleanup to remove any duplicate gyms
	if user.Role == "member" {
		go h.cleanupDuplicateGymsForUser(c.Request.Context(), token.UID)
	}

	c.JSON(http.StatusOK, gin.H{
		"user": user,
		"uid":  token.UID,
	})
}

// RefreshToken handles token refresh (placeholder for now)
func (h *AuthHandler) RefreshToken(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "Token refresh not implemented yet"})
}

// getOrCreateUser retrieves or creates a user record in Firestore
func (h *AuthHandler) getOrCreateUser(ctx context.Context, uid string, claims map[string]interface{}) (*models.User, error) {
	// This function is now deprecated - we handle user and gym creation in VerifyToken
	// Just retrieve the user if it exists
	userDoc, err := h.firebaseService.GetDocument(ctx, "users", uid)
	if err != nil {
		// User doesn't exist, this should be handled by VerifyToken
		return nil, err
	}

	// User exists, return existing user
	var user models.User
	if err := userDoc.DataTo(&user); err != nil {
		return nil, err
	}

	return &user, nil
}

// VerifyInvitation verifies a member invitation
func (h *AuthHandler) VerifyInvitation(c *gin.Context) {
	var request struct {
		MemberID   string `json:"memberId" binding:"required"`
		AccessCode string `json:"accessCode" binding:"required"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get the invitation
	inviteDoc, err := h.firebaseService.GetDocument(c.Request.Context(), "member_invites", request.MemberID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Invitation not found"})
		return
	}

	// Debug: Print raw data
	log.Printf("DEBUG: Raw invitation data: %+v", inviteDoc.Data())

	var invite struct {
		AccessCode string    `firestore:"accessCode"`
		ExpiresAt  time.Time `firestore:"expiresAt"`
		Email      string    `firestore:"email"`
		MemberID   string    `firestore:"memberID"`
		GymID      string    `firestore:"gymId"` // Changed to match expected Firestore field
	}

	if err := inviteDoc.DataTo(&invite); err != nil {
		log.Printf("ERROR: Failed to parse invitation: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to parse invitation"})
		return
	}

	// Verify access code
	if invite.AccessCode != request.AccessCode {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid access code"})
		return
	}

	// Check if invitation has expired
	if time.Now().After(invite.ExpiresAt) {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invitation has expired"})
		return
	}

	// Get member data
	memberDoc, err := h.firebaseService.GetDocument(c.Request.Context(), "members", request.MemberID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Member not found"})
		return
	}

	var member models.Member
	if err := memberDoc.DataTo(&member); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to parse member data"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"member": map[string]interface{}{
			"id":    request.MemberID,
			"email": member.Email,
			"name":  member.FirstName + " " + member.LastName,
			"gymId": member.GymID,
		},
	})
}

// LinkMemberAccount links a Firebase user to a member
func (h *AuthHandler) LinkMemberAccount(c *gin.Context) {
	log.Printf("DEBUG: LinkMemberAccount called")

	var request struct {
		MemberID   string `json:"memberId" binding:"required"`
		AccessCode string `json:"accessCode" binding:"required"`
		UserUID    string `json:"userUid" binding:"required"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		log.Printf("ERROR: Invalid request: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	log.Printf("DEBUG: Request data: memberId=%s, accessCode=%s, userUid=%s",
		request.MemberID, request.AccessCode, request.UserUID)

	// Verify invitation again
	inviteDoc, err := h.firebaseService.GetDocument(c.Request.Context(), "member_invites", request.MemberID)
	if err != nil {
		log.Printf("ERROR: Invitation not found: %v", err)
		c.JSON(http.StatusNotFound, gin.H{"error": "Invitation not found"})
		return
	}

	// Debug: Print raw data
	log.Printf("DEBUG: Raw invitation data: %+v", inviteDoc.Data())

	var invite struct {
		AccessCode string    `firestore:"accessCode"`
		ExpiresAt  time.Time `firestore:"expiresAt"`
		GymID      string    `firestore:"gymId"` // Changed to match Firestore field name
		Email      string    `firestore:"email"`
	}

	if err := inviteDoc.DataTo(&invite); err != nil {
		log.Printf("ERROR: Failed to parse invitation: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to parse invitation"})
		return
	}

	log.Printf("DEBUG: Invitation data: accessCode=%s, expiresAt=%v, gymId=%s, email=%s",
		invite.AccessCode, invite.ExpiresAt, invite.GymID, invite.Email)

	if invite.AccessCode != request.AccessCode {
		log.Printf("ERROR: Invalid access code: expected=%s, got=%s", invite.AccessCode, request.AccessCode)
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid access code"})
		return
	}

	if time.Now().After(invite.ExpiresAt) {
		log.Printf("ERROR: Invitation expired: expiresAt=%v, now=%v", invite.ExpiresAt, time.Now())
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invitation has expired"})
		return
	}

	// Get member data
	memberDoc, err := h.firebaseService.GetDocument(c.Request.Context(), "members", request.MemberID)
	if err != nil {
		log.Printf("ERROR: Member not found: %v", err)
		c.JSON(http.StatusNotFound, gin.H{"error": "Member not found"})
		return
	}

	log.Printf("DEBUG: Found member document")

	var member models.Member
	if err := memberDoc.DataTo(&member); err != nil {
		log.Printf("ERROR: Failed to parse member data: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to parse member data"})
		return
	}

	log.Printf("DEBUG: Member data: firstName=%s, lastName=%s, email=%s, gymId=%s",
		member.FirstName, member.LastName, member.Email, member.GymID)

	// Instead of using a transaction, perform operations sequentially
	// 1. Update member with user UID
	_, err = h.firebaseService.Collection("members").Doc(request.MemberID).Update(c.Request.Context(), []firestore.Update{
		{Path: "userUid", Value: request.UserUID},
		{Path: "updatedAt", Value: time.Now()},
	})

	if err != nil {
		log.Printf("ERROR: Failed to update member: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to link account"})
		return
	}

	log.Printf("DEBUG: Updated member with userUid=%s", request.UserUID)

	// 2. Create a user record with role "member"
	userData := map[string]interface{}{
		"uid":       request.UserUID,
		"email":     invite.Email,
		"name":      member.FirstName + " " + member.LastName,
		"gymId":     invite.GymID, // Using the gymId from the invitation
		"role":      "member",
		"createdAt": time.Now(),
		"updatedAt": time.Now(),
	}

	log.Printf("DEBUG: Creating user record: %+v", userData)

	// Check if user document already exists
	_, err = h.firebaseService.Collection("users").Doc(request.UserUID).Get(c.Request.Context())
	if err == nil {
		// User already exists, update instead of create
		log.Printf("DEBUG: User document already exists, updating instead")
		_, err = h.firebaseService.Collection("users").Doc(request.UserUID).Update(c.Request.Context(), []firestore.Update{
			{Path: "gymId", Value: invite.GymID},
			{Path: "role", Value: "member"},
			{Path: "name", Value: member.FirstName + " " + member.LastName},
			{Path: "updatedAt", Value: time.Now()},
		})
	} else {
		// User doesn't exist, create new document
		_, err = h.firebaseService.Collection("users").Doc(request.UserUID).Set(c.Request.Context(), userData)
	}

	if err != nil {
		log.Printf("ERROR: Failed to create/update user record: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create user record"})
		return
	}

	log.Printf("DEBUG: Created/updated user record successfully")

	// 3. Delete the invitation
	_, err = h.firebaseService.Collection("member_invites").Doc(request.MemberID).Delete(c.Request.Context())
	if err != nil {
		// Non-critical error, just log it
		log.Printf("WARNING: Failed to delete invitation: %v", err)
	} else {
		log.Printf("DEBUG: Deleted invitation successfully")
	}

	c.JSON(http.StatusOK, gin.H{"message": "Account linked successfully"})
	log.Printf("DEBUG: LinkMemberAccount completed successfully")
}

// RegisterMember registers a new member account
func (h *AuthHandler) RegisterMember(c *gin.Context) {
	var request struct {
		Email      string `json:"email" binding:"required,email"`
		Password   string `json:"password" binding:"required"`
		MemberID   string `json:"memberId" binding:"required"`
		AccessCode string `json:"accessCode" binding:"required"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Verify invitation
	inviteDoc, err := h.firebaseService.GetDocument(c.Request.Context(), "member_invites", request.MemberID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Invitation not found"})
		return
	}

	var invite struct {
		AccessCode string    `firestore:"accessCode"`
		ExpiresAt  time.Time `firestore:"expiresAt"`
		GymID      string    `firestore:"gymID"`
	}

	if err := inviteDoc.DataTo(&invite); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to parse invitation"})
		return
	}

	if invite.AccessCode != request.AccessCode || time.Now().After(invite.ExpiresAt) {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid or expired invitation"})
		return
	}

	// Create Firebase user
	params := (&auth.UserToCreate{}).
		Email(request.Email).
		Password(request.Password)

	userRecord, err := h.firebaseService.Auth.CreateUser(c.Request.Context(), params)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create user account"})
		return
	}

	// Link member account
	err = h.linkMemberAccount(c.Request.Context(), request.MemberID, userRecord.UID, invite.GymID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "Member account created successfully",
		"uid":     userRecord.UID,
	})
}

// linkMemberAccount links a Firebase user to a member
func (h *AuthHandler) linkMemberAccount(ctx context.Context, memberID, userUID, gymID string) error {
	// Get member data
	memberDoc, err := h.firebaseService.GetDocument(ctx, "members", memberID)
	if err != nil {
		return fmt.Errorf("member not found")
	}

	var member models.Member
	if err := memberDoc.DataTo(&member); err != nil {
		return fmt.Errorf("failed to parse member data")
	}

	// 1. Update member with user UID
	_, err = h.firebaseService.Collection("members").Doc(memberID).Update(ctx, []firestore.Update{
		{Path: "userUid", Value: userUID},
		{Path: "updatedAt", Value: time.Now()},
	})

	if err != nil {
		return fmt.Errorf("failed to link account: %w", err)
	}

	// 2. Create a user record with role "member"
	userData := map[string]interface{}{
		"uid":       userUID,
		"email":     member.Email,
		"name":      member.FirstName + " " + member.LastName,
		"gymId":     gymID,
		"role":      "member",
		"createdAt": time.Now(),
		"updatedAt": time.Now(),
	}

	// Check if user document already exists
	_, err = h.firebaseService.Collection("users").Doc(userUID).Get(ctx)
	if err == nil {
		// User already exists, update instead of create
		_, err = h.firebaseService.Collection("users").Doc(userUID).Update(ctx, []firestore.Update{
			{Path: "gymId", Value: gymID},
			{Path: "role", Value: "member"},
			{Path: "name", Value: member.FirstName + " " + member.LastName},
			{Path: "updatedAt", Value: time.Now()},
		})
	} else {
		// User doesn't exist, create new document
		_, err = h.firebaseService.Collection("users").Doc(userUID).Set(ctx, userData)
	}

	if err != nil {
		return fmt.Errorf("failed to create user record: %w", err)
	}

	// 3. Delete the invitation
	_, err = h.firebaseService.Collection("member_invites").Doc(memberID).Delete(ctx)
	if err != nil {
		// Non-critical error, just log it
		log.Printf("Failed to delete invitation: %v", err)
	}

	return nil
}

// cleanupDuplicateGymsForUser removes any gyms created for a member
// This is a non-blocking version that can be called with go routine
func (h *AuthHandler) cleanupDuplicateGymsForUser(ctx context.Context, userUID string) {
	// Get user data
	userDoc, err := h.firebaseService.GetDocument(ctx, "users", userUID)
	if err != nil {
		log.Printf("Cleanup failed: User %s not found", userUID)
		return
	}

	var user models.User
	if err := userDoc.DataTo(&user); err != nil {
		log.Printf("Cleanup failed: Could not parse user data for %s", userUID)
		return
	}

	// If user is a member, find and delete any gyms they own
	if user.Role == "member" {
		gymQuery := h.firebaseService.Collection("gyms").Where("ownerUid", "==", userUID)
		gymDocs, err := gymQuery.Documents(ctx).GetAll()

		if err != nil {
			log.Printf("Cleanup failed: Could not query gyms for user %s: %v", userUID, err)
			return
		}

		deletedCount := 0
		for _, doc := range gymDocs {
			// Skip the gym that the member belongs to
			if doc.Ref.ID == user.GymID {
				continue
			}

			_, err := h.firebaseService.Collection("gyms").Doc(doc.Ref.ID).Delete(ctx)
			if err != nil {
				log.Printf("Failed to delete gym %s: %v", doc.Ref.ID, err)
				continue
			}
			deletedCount++
		}

		log.Printf("Cleanup completed for user %s: Deleted %d gyms", userUID, deletedCount)
	}
}
