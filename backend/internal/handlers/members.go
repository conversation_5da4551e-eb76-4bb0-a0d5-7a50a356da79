// handlers/members.go
package handlers

import (
	"fmt"
	"log"
	"math/rand"
	"net/http"
	"time"

	"dojofy/internal/models"
	"dojofy/internal/services"

	"cloud.google.com/go/firestore"
	"github.com/gin-gonic/gin"
	"google.golang.org/api/iterator"
)

// MemberHandler handles member-related requests
type MemberHandler struct {
	firebaseService *services.FirebaseService
	emailService    *services.EmailService
}

// NewMemberHandler creates a new member handler
func NewMemberHandler(firebaseService *services.FirebaseService, emailService *services.EmailService) *MemberHandler {
	return &MemberHandler{
		firebaseService: firebaseService,
		emailService:    emailService,
	}
}

// GetAllMembers retrieves all members for the authenticated user's gym
func (h *MemberHandler) GetAllMembers(c *gin.Context) {
	gymID, exists := c.Get("gymID")
	if !exists || gymID == "" {
		c.<PERSON>(http.StatusForbidden, gin.H{"error": "No gym access"})
		return
	}

	// Debug log
	fmt.Printf("Fetching members for gym: %s\n", gymID)

	// Query members collection filtered by gymId
	iter := h.firebaseService.Collection("members").Where("gymId", "==", gymID).Documents(c.Request.Context())

	var members []models.Member
	for {
		doc, err := iter.Next()
		if err == iterator.Done {
			break
		}
		if err != nil {
			fmt.Printf("Error fetching members: %v\n", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch members"})
			return
		}

		var member models.Member
		if err := doc.DataTo(&member); err != nil {
			fmt.Printf("Error parsing member data: %v\n", err)
			continue // Skip invalid documents
		}
		member.ID = doc.Ref.ID
		members = append(members, member)
	}

	fmt.Printf("Found %d members\n", len(members))
	c.JSON(http.StatusOK, gin.H{"members": members})
}

// GetMemberByID retrieves a specific member by ID
func (h *MemberHandler) GetMemberByID(c *gin.Context) {
	memberID := c.Param("id")
	gymID, exists := c.Get("gymID")
	if !exists || gymID == "" {
		c.JSON(http.StatusForbidden, gin.H{"error": "No gym access"})
		return
	}

	doc, err := h.firebaseService.GetDocument(c.Request.Context(), "members", memberID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Member not found"})
		return
	}

	var member models.Member
	if err := doc.DataTo(&member); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to parse member data"})
		return
	}

	// Verify member belongs to user's gym
	if member.GymID != gymID {
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
		return
	}

	member.ID = doc.Ref.ID
	c.JSON(http.StatusOK, gin.H{"member": member})
}

// CreateMember creates a new member
func (h *MemberHandler) CreateMember(c *gin.Context) {
	var request models.CreateMemberRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	gymID, exists := c.Get("gymID")
	if !exists || gymID == "" {
		c.JSON(http.StatusForbidden, gin.H{"error": "No gym access"})
		return
	}

	// Verify membership plan exists and belongs to user's gym
	if request.MembershipPlanID != "" {
		planDoc, err := h.firebaseService.GetDocument(c.Request.Context(), "membership_plans", request.MembershipPlanID)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid membership plan"})
			return
		}

		var plan models.MembershipPlan
		if err := planDoc.DataTo(&plan); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to parse plan data"})
			return
		}

		if plan.GymID != gymID {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied to membership plan"})
			return
		}
	}

	// Create new member
	member := models.Member{
		FirstName:        request.FirstName,
		LastName:         request.LastName,
		Email:            request.Email,
		Phone:            request.Phone,
		PaymentMethod:    request.PaymentMethod,
		MartialArt:       request.MartialArt,
		MembershipPlanID: request.MembershipPlanID,
		ProfilePicURL:    "", // Empty for now as specified
		JoinDate:         time.Now(),
		IsActive:         true,
		GymID:            gymID.(string),
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
	}

	// Add to Firestore
	docRef, _, err := h.firebaseService.Collection("members").Add(c.Request.Context(), member)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create member"})
		return
	}

	member.ID = docRef.ID
	c.JSON(http.StatusCreated, gin.H{"member": member})
}

// UpdateMember updates an existing member
func (h *MemberHandler) UpdateMember(c *gin.Context) {
	memberID := c.Param("id")
	var request models.UpdateMemberRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	gymID, exists := c.Get("gymID")
	if !exists || gymID == "" {
		c.JSON(http.StatusForbidden, gin.H{"error": "No gym access"})
		return
	}

	// Verify member exists and belongs to user's gym
	doc, err := h.firebaseService.GetDocument(c.Request.Context(), "members", memberID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Member not found"})
		return
	}

	var existingMember models.Member
	if err := doc.DataTo(&existingMember); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to parse member data"})
		return
	}

	if existingMember.GymID != gymID {
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
		return
	}

	// Verify membership plan if provided
	if request.MembershipPlanID != "" {
		planDoc, err := h.firebaseService.GetDocument(c.Request.Context(), "membership_plans", request.MembershipPlanID)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid membership plan"})
			return
		}

		var plan models.MembershipPlan
		if err := planDoc.DataTo(&plan); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to parse plan data"})
			return
		}

		if plan.GymID != gymID {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied to membership plan"})
			return
		}
	}

	// Build update map
	updates := []firestore.Update{
		{Path: "updatedAt", Value: time.Now()},
	}

	if request.FirstName != "" {
		updates = append(updates, firestore.Update{Path: "firstName", Value: request.FirstName})
	}
	if request.LastName != "" {
		updates = append(updates, firestore.Update{Path: "lastName", Value: request.LastName})
	}
	if request.Email != "" {
		updates = append(updates, firestore.Update{Path: "email", Value: request.Email})
	}
	if request.Phone != "" {
		updates = append(updates, firestore.Update{Path: "phone", Value: request.Phone})
	}
	if request.PaymentMethod != "" {
		updates = append(updates, firestore.Update{Path: "paymentMethod", Value: request.PaymentMethod})
	}
	if request.MartialArt != "" {
		updates = append(updates, firestore.Update{Path: "martialArt", Value: request.MartialArt})
	}
	if request.MembershipPlanID != "" {
		updates = append(updates, firestore.Update{Path: "membershipPlanId", Value: request.MembershipPlanID})
	}
	if request.IsActive != nil {
		updates = append(updates, firestore.Update{Path: "isActive", Value: *request.IsActive})
	}

	// Update in Firestore
	if err := h.firebaseService.UpdateDocument(c.Request.Context(), "members", memberID, updates); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update member"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Member updated successfully"})
}

// DeleteMember deletes a member
func (h *MemberHandler) DeleteMember(c *gin.Context) {
	memberID := c.Param("id")
	gymID, exists := c.Get("gymID")
	if !exists || gymID == "" {
		c.JSON(http.StatusForbidden, gin.H{"error": "No gym access"})
		return
	}

	// Verify member exists and belongs to user's gym
	doc, err := h.firebaseService.GetDocument(c.Request.Context(), "members", memberID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Member not found"})
		return
	}

	var member models.Member
	if err := doc.DataTo(&member); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to parse member data"})
		return
	}

	if member.GymID != gymID {
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
		return
	}

	// Delete from Firestore
	if err := h.firebaseService.DeleteDocument(c.Request.Context(), "members", memberID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete member"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Member deleted successfully"})
}

// SendMemberInvitation sends an invitation email to a member
func (h *MemberHandler) SendMemberInvitation(c *gin.Context) {
	memberID := c.Param("id")
	gymID, exists := c.Get("gymID")

	if !exists || gymID == "" {
		c.JSON(http.StatusForbidden, gin.H{"error": "No gym access"})
		return
	}

	// Verify member exists and belongs to user's gym
	doc, err := h.firebaseService.GetDocument(c.Request.Context(), "members", memberID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Member not found"})
		return
	}

	var member models.Member
	if err := doc.DataTo(&member); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to parse member data"})
		return
	}

	if member.GymID != gymID.(string) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
		return
	}

	// Get gym info for the email
	gymDoc, err := h.firebaseService.GetDocument(c.Request.Context(), "gyms", gymID.(string))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get gym info"})
		return
	}

	var gym struct {
		Name string `firestore:"name"`
	}
	if err := gymDoc.DataTo(&gym); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to parse gym data"})
		return
	}

	// Generate a unique access code for this member
	accessCode := generateAccessCode()

	// Debug: Print the invitation data being stored
	log.Printf("DEBUG: Storing invitation with memberID=%s, gymID=%s, accessCode=%s, email=%s",
		memberID, gymID, accessCode, member.Email)

	// Store the access code in Firestore
	_, err = h.firebaseService.Collection("member_invites").Doc(memberID).Set(c.Request.Context(), map[string]interface{}{
		"accessCode": accessCode,
		"email":      member.Email,
		"memberID":   memberID,
		"gymId":      gymID,                              // Note the capitalization here
		"expiresAt":  time.Now().Add(7 * 24 * time.Hour), // 7 days expiration
		"createdAt":  time.Now(),
	})

	if err != nil {
		log.Printf("ERROR: Failed to create invitation: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create invitation"})
		return
	}

	// Create invitation link
	inviteLink := fmt.Sprintf("%s/register?memberId=%s&accessCode=%s",
		c.GetHeader("Origin"), memberID, accessCode)

	// Send email invitation
	memberName := fmt.Sprintf("%s %s", member.FirstName, member.LastName)

	// Check if email service is initialized
	if h.emailService == nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message":    "Invitation created but email service not available",
			"inviteLink": inviteLink,
			"emailError": "Email service not initialized",
		})
		return
	}

	err = h.emailService.SendMemberInvitation(member.Email, memberName, gym.Name, inviteLink)
	if err != nil {
		// Don't fail the request, but include the error in the response
		c.JSON(http.StatusOK, gin.H{
			"message":    "Invitation created but email could not be sent",
			"inviteLink": inviteLink,
			"emailError": err.Error(),
		})
		return
	}

	// Return the link in the response
	c.JSON(http.StatusOK, gin.H{
		"message":    "Invitation created and email sent successfully",
		"inviteLink": inviteLink,
	})
}

// Helper function to generate a random access code
func generateAccessCode() string {
	const charset = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	code := make([]byte, 8)
	for i := range code {
		code[i] = charset[rand.Intn(len(charset))]
	}
	return string(code)
}
