// handlers/classes.go
package handlers

import (
	"log"
	"net/http"
	"time"

	"dojofy/internal/models"
	"dojofy/internal/services"

	"cloud.google.com/go/firestore"
	"github.com/gin-gonic/gin"
	"google.golang.org/api/iterator"
)

// ClassHandler handles class-related requests
type ClassHandler struct {
	firebaseService *services.FirebaseService
}

// NewClassHandler creates a new class handler
func NewClassHandler(firebaseService *services.FirebaseService) *ClassHandler {
	return &ClassHandler{
		firebaseService: firebaseService,
	}
}

// GetAllClasses retrieves all classes for the authenticated user's gym
func (h *ClassHandler) GetAllClasses(c *gin.Context) {
	gymID, exists := c.Get("gymID")
	if !exists || gymID == "" {
		c.JSON(http.StatusForbidden, gin.H{"error": "No gym access"})
		return
	}

	iter := h.firebaseService.Collection("classes").Where("gymId", "==", gymID).Documents(c.Request.Context())

	var classes []models.WeeklyClass
	for {
		doc, err := iter.Next()
		if err == iterator.Done {
			break
		}
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch classes"})
			return
		}

		var class models.WeeklyClass
		if err := doc.DataTo(&class); err != nil {
			continue
		}
		class.ID = doc.Ref.ID
		classes = append(classes, class)
	}

	c.JSON(http.StatusOK, gin.H{"classes": classes})
}

// GetClassByID retrieves a specific class by ID
func (h *ClassHandler) GetClassByID(c *gin.Context) {
	classID := c.Param("id")
	gymID, exists := c.Get("gymID")
	if !exists || gymID == "" {
		c.JSON(http.StatusForbidden, gin.H{"error": "No gym access"})
		return
	}

	doc, err := h.firebaseService.GetDocument(c.Request.Context(), "classes", classID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Class not found"})
		return
	}

	var class models.WeeklyClass
	if err := doc.DataTo(&class); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to parse class data"})
		return
	}

	if class.GymID != gymID {
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
		return
	}

	class.ID = doc.Ref.ID
	c.JSON(http.StatusOK, gin.H{"class": class})
}

// CreateClass creates a new class
func (h *ClassHandler) CreateClass(c *gin.Context) {
	var request models.CreateClassRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	gymID, exists := c.Get("gymID")
	if !exists || gymID == "" {
		c.JSON(http.StatusForbidden, gin.H{"error": "No gym access"})
		return
	}

	class := models.WeeklyClass{
		Name:        request.Name,
		Type:        request.Type,
		DayOfWeek:   request.DayOfWeek,
		StartTime:   request.StartTime,
		EndTime:     request.EndTime,
		Coach:       request.Coach,
		MaxSize:     request.MaxSize,
		CurrentSize: 0,
		StartDate:   request.StartDate,
		IsActive:    true,
		GymID:       gymID.(string),
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	docRef, _, err := h.firebaseService.Collection("classes").Add(c.Request.Context(), class)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create class"})
		return
	}

	class.ID = docRef.ID
	c.JSON(http.StatusCreated, gin.H{"class": class})
}

// UpdateClass updates an existing class
func (h *ClassHandler) UpdateClass(c *gin.Context) {
	classID := c.Param("id")
	var request models.UpdateClassRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	gymID, exists := c.Get("gymID")
	if !exists || gymID == "" {
		c.JSON(http.StatusForbidden, gin.H{"error": "No gym access"})
		return
	}

	// Verify class exists and belongs to user's gym
	doc, err := h.firebaseService.GetDocument(c.Request.Context(), "classes", classID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Class not found"})
		return
	}

	var existingClass models.WeeklyClass
	if err := doc.DataTo(&existingClass); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to parse class data"})
		return
	}

	if existingClass.GymID != gymID {
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
		return
	}

	// Build update map
	updates := []firestore.Update{
		{Path: "updatedAt", Value: time.Now()},
	}

	if request.Name != "" {
		updates = append(updates, firestore.Update{Path: "name", Value: request.Name})
	}
	if request.Type != "" {
		updates = append(updates, firestore.Update{Path: "type", Value: request.Type})
	}
	if request.DayOfWeek != nil {
		updates = append(updates, firestore.Update{Path: "dayOfWeek", Value: *request.DayOfWeek})
	}
	if request.StartTime != "" {
		updates = append(updates, firestore.Update{Path: "startTime", Value: request.StartTime})
	}
	if request.EndTime != "" {
		updates = append(updates, firestore.Update{Path: "endTime", Value: request.EndTime})
	}
	if request.Coach != "" {
		updates = append(updates, firestore.Update{Path: "coach", Value: request.Coach})
	}
	if request.MaxSize != nil {
		updates = append(updates, firestore.Update{Path: "maxSize", Value: *request.MaxSize})
	}
	if request.StartDate != nil {
		updates = append(updates, firestore.Update{Path: "startDate", Value: *request.StartDate})
	}
	if request.IsActive != nil {
		updates = append(updates, firestore.Update{Path: "isActive", Value: *request.IsActive})
	}

	if err := h.firebaseService.UpdateDocument(c.Request.Context(), "classes", classID, updates); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update class"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Class updated successfully"})
}

// DeleteClass deletes a class
func (h *ClassHandler) DeleteClass(c *gin.Context) {
	classID := c.Param("id")
	gymID, exists := c.Get("gymID")
	if !exists || gymID == "" {
		c.JSON(http.StatusForbidden, gin.H{"error": "No gym access"})
		return
	}

	doc, err := h.firebaseService.GetDocument(c.Request.Context(), "classes", classID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Class not found"})
		return
	}

	var class models.WeeklyClass
	if err := doc.DataTo(&class); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to parse class data"})
		return
	}

	if class.GymID != gymID {
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
		return
	}

	if err := h.firebaseService.DeleteDocument(c.Request.Context(), "classes", classID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete class"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Class deleted successfully"})
}

// GetClassInstances retrieves class instances for a date range
func (h *ClassHandler) GetClassInstances(c *gin.Context) {
	log.Printf("GetClassInstances called with context: %v", c.Request.URL)

	gymID, exists := c.Get("gymID")
	if !exists || gymID == "" {
		log.Printf("No gym ID found in context")
		c.JSON(http.StatusForbidden, gin.H{"error": "No gym access"})
		return
	}
	log.Printf("Getting instances for gym ID: %s", gymID)

	// Accept startDate and endDate as optional query params (YYYY-MM-DD)
	startDateStr := c.Query("startDate")
	endDateStr := c.Query("endDate")

	var iter *firestore.DocumentIterator

	if startDateStr != "" && endDateStr != "" {
		// Validate date format
		_, err := time.Parse("2006-01-02", startDateStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid startDate format. Use YYYY-MM-DD."})
			return
		}
		_, err = time.Parse("2006-01-02", endDateStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid endDate format. Use YYYY-MM-DD."})
			return
		}

		// Query Firestore with date range
		iter = h.firebaseService.Collection("class_instances").
			Where("gymId", "==", gymID).
			Where("date", ">=", startDateStr).
			Where("date", "<=", endDateStr).
			Documents(c.Request.Context())
	} else {
		// Query Firestore without date range - get all instances for the gym
		iter = h.firebaseService.Collection("class_instances").
			Where("gymId", "==", gymID).
			Documents(c.Request.Context())
	}

	var instances []map[string]interface{}
	for {
		doc, err := iter.Next()
		if err == iterator.Done {
			break
		}
		if err != nil {
			log.Printf("Error fetching instance: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch class instances"})
			return
		}
		instance := doc.Data()
		instance["id"] = doc.Ref.ID

		// Convert time.Time fields to ISO8601 strings for proper JSON serialization
		if startTime, ok := instance["startTime"].(time.Time); ok {
			instance["startTime"] = startTime.Format(time.RFC3339)
		}
		if endTime, ok := instance["endTime"].(time.Time); ok {
			instance["endTime"] = endTime.Format(time.RFC3339)
		}
		if createdAt, ok := instance["createdAt"].(time.Time); ok {
			instance["createdAt"] = createdAt.Format(time.RFC3339)
		}
		if updatedAt, ok := instance["updatedAt"].(time.Time); ok {
			instance["updatedAt"] = updatedAt.Format(time.RFC3339)
		}

		instances = append(instances, instance)
	}

	log.Printf("Found %d instances", len(instances))
	c.JSON(http.StatusOK, gin.H{"instances": instances})
}

// GenerateClassInstances generates class instances for the upcoming weeks
func (h *ClassHandler) GenerateClassInstances(c *gin.Context) {
	gymID, exists := c.Get("gymID")
	if !exists || gymID == "" {
		c.JSON(http.StatusForbidden, gin.H{"error": "No gym access"})
		return
	}

	// Parse request body
	var request struct {
		WeeksAhead int `json:"weeksAhead"`
	}
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Default to 8 weeks if not specified or invalid
	if request.WeeksAhead <= 0 {
		request.WeeksAhead = 8
	}

	// Get all active class templates
	iter := h.firebaseService.Collection("classes").
		Where("gymId", "==", gymID).
		Where("isActive", "==", true).
		Documents(c.Request.Context())

	var templates []models.WeeklyClass
	for {
		doc, err := iter.Next()
		if err == iterator.Done {
			break
		}
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch class templates"})
			return
		}

		var template models.WeeklyClass
		if err := doc.DataTo(&template); err != nil {
			continue
		}
		template.ID = doc.Ref.ID
		templates = append(templates, template)
	}

	// Generate instances for each template
	now := time.Now()
	endDate := now.AddDate(0, 0, request.WeeksAhead*7)

	// Create a batch using the Firestore client directly
	batch := h.firebaseService.Firestore.Batch()
	instancesCollection := h.firebaseService.Collection("class_instances")

	generatedCount := 0

	for _, template := range templates {
		// Skip if template start date is in the future
		if template.StartDate.After(endDate) {
			continue
		}

		// Calculate first instance date based on day of week
		currentDate := now
		// Adjust to the next occurrence of the template's day of week
		daysUntilNext := (template.DayOfWeek - int(currentDate.Weekday()) + 7) % 7
		if daysUntilNext == 0 && currentDate.Hour() >= 23 {
			daysUntilNext = 7 // If it's already late in the day, move to next week
		}
		currentDate = currentDate.AddDate(0, 0, daysUntilNext)

		// Generate instances until end date
		for currentDate.Before(endDate) {
			// Check if instance already exists
			existingQuery := instancesCollection.
				Where("templateId", "==", template.ID).
				Where("date", "==", currentDate.Format("2006-01-02")).
				Limit(1).
				Documents(c.Request.Context())

			_, err := existingQuery.Next()
			if err != iterator.Done {
				// Instance already exists, skip
				currentDate = currentDate.AddDate(0, 0, 7)
				continue
			}

			startTime, err := time.Parse("15:04", template.StartTime)
			if err != nil {
				c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid start time format"})
				return
			}
			endTime, err := time.Parse("15:04", template.EndTime)
			if err != nil {
				c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid end time format"})
				return
			}

			startTime = time.Date(currentDate.Year(), currentDate.Month(), currentDate.Day(), startTime.Hour(), startTime.Minute(), 0, 0, currentDate.Location())
			endTime = time.Date(currentDate.Year(), currentDate.Month(), currentDate.Day(), endTime.Hour(), endTime.Minute(), 0, 0, currentDate.Location())

			// Create new instance
			instance := models.ClassInstance{
				TemplateID:    template.ID,
				GymID:         template.GymID,
				Name:          template.Name,
				Type:          template.Type,
				Date:          currentDate.Format("2006-01-02"),
				StartTime:     startTime,
				EndTime:       endTime,
				Coach:         template.Coach,
				MaxCapacity:   template.MaxSize,
				EnrolledCount: 0,
				WaitlistCount: 0,
				Status:        "scheduled",
				CreatedAt:     time.Now(),
			}

			// Add to batch
			newRef := instancesCollection.NewDoc()
			batch.Set(newRef, instance)
			generatedCount++

			// Move to next week
			currentDate = currentDate.AddDate(0, 0, 7)
		}
	}

	// Commit batch if there are instances to create
	if generatedCount > 0 {
		_, err := batch.Commit(c.Request.Context())
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate class instances"})
			return
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Class instances generated successfully",
		"count":   generatedCount,
	})
}

// UpdateClassInstance updates a specific class instance
func (h *ClassHandler) UpdateClassInstance(c *gin.Context) {
	instanceID := c.Param("id")
	gymID, exists := c.Get("gymID")
	if !exists || gymID == "" {
		c.JSON(http.StatusForbidden, gin.H{"error": "No gym access"})
		return
	}

	// Get the instance
	doc, err := h.firebaseService.GetDocument(c.Request.Context(), "class_instances", instanceID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Class instance not found"})
		return
	}

	// Verify gym ownership
	instance := doc.Data()
	if instance["gymId"] != gymID {
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
		return
	}

	// Parse request
	var request struct {
		Coach       string `json:"coach"`
		StartTime   string `json:"startTime"`
		EndTime     string `json:"endTime"`
		MaxCapacity int    `json:"maxCapacity"`
		Status      string `json:"status"`
	}
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Build updates
	updates := []firestore.Update{
		{Path: "updatedAt", Value: time.Now()},
	}

	if request.Coach != "" {
		updates = append(updates, firestore.Update{Path: "coach", Value: request.Coach})
	}
	if request.StartTime != "" {
		updates = append(updates, firestore.Update{Path: "startTime", Value: request.StartTime})
	}
	if request.EndTime != "" {
		updates = append(updates, firestore.Update{Path: "endTime", Value: request.EndTime})
	}
	if request.MaxCapacity > 0 {
		updates = append(updates, firestore.Update{Path: "maxCapacity", Value: request.MaxCapacity})
	}
	if request.Status != "" {
		updates = append(updates, firestore.Update{Path: "status", Value: request.Status})
	}

	// Update the document
	if err := h.firebaseService.UpdateDocument(c.Request.Context(), "class_instances", instanceID, updates); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update class instance"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Class instance updated successfully"})
}

// CancelClassInstance cancels a specific class instance
func (h *ClassHandler) CancelClassInstance(c *gin.Context) {
	instanceID := c.Param("id")
	gymID, exists := c.Get("gymID")
	if !exists || gymID == "" {
		c.JSON(http.StatusForbidden, gin.H{"error": "No gym access"})
		return
	}

	// Get the instance
	doc, err := h.firebaseService.GetDocument(c.Request.Context(), "class_instances", instanceID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Class instance not found"})
		return
	}

	// Verify gym ownership
	instance := doc.Data()
	if instance["gymId"] != gymID {
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
		return
	}

	// Parse request
	var request struct {
		Reason string `json:"reason"`
	}
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Update the document
	updates := []firestore.Update{
		{Path: "status", Value: "cancelled"},
		{Path: "cancellationReason", Value: request.Reason},
		{Path: "updatedAt", Value: time.Now()},
	}

	if err := h.firebaseService.UpdateDocument(c.Request.Context(), "class_instances", instanceID, updates); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to cancel class instance"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Class instance cancelled successfully"})
}
