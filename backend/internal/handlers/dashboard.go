package handlers

import (
	"context"
	"log"
	"net/http"
	"time"

	"dojofy/internal/models"
	"dojofy/internal/services"

	"github.com/gin-gonic/gin"
	"google.golang.org/api/iterator"
)

// DashboardHandler handles dashboard and insights requests
type DashboardHandler struct {
	firebaseService *services.FirebaseService
}

// NewDashboardHandler creates a new dashboard handler
func NewDashboardHandler(firebaseService *services.FirebaseService) *DashboardHandler {
	return &DashboardHandler{
		firebaseService: firebaseService,
	}
}

// GetBasicStats returns basic statistics for the dashboard
func (h *DashboardHandler) GetBasicStats(c *gin.Context) {
	gymID, exists := c.Get("gymID")
	if !exists || gymID == "" {
		c.JSON(http.StatusForbidden, gin.H{"error": "No gym access"})
		return
	}

	ctx := c.Request.Context()

	// Get member counts
	totalMembers, activeMembers, err := h.getMemberCounts(ctx, gymID.(string))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch member statistics"})
		return
	}

	// Get class counts
	totalClasses, activeClasses, err := h.getClassCounts(ctx, gymID.(string))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch class statistics"})
		return
	}

	stats := models.DashboardStats{
		TotalMembers:  totalMembers,
		ActiveMembers: activeMembers,
		TotalClasses:  totalClasses,
		ActiveClasses: activeClasses,
	}

	c.JSON(http.StatusOK, gin.H{"stats": stats})
}

// GetAllInsights returns comprehensive insights data in a single response
func (h *DashboardHandler) GetAllInsights(c *gin.Context) {
	gymID, exists := c.Get("gymID")
	if !exists || gymID == "" {
		c.JSON(http.StatusForbidden, gin.H{"error": "No gym access"})
		return
	}

	ctx := c.Request.Context()
	gymIDStr := gymID.(string)

	// Get basic dashboard stats
	totalMembers, activeMembers, err := h.getMemberCounts(ctx, gymIDStr)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch member statistics"})
		return
	}

	totalClasses, activeClasses, err := h.getClassCounts(ctx, gymIDStr)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch class statistics"})
		return
	}

	// Calculate member growth stats with better error handling
	now := time.Now()
	startOfCurrentMonth := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	startOfLastMonth := startOfCurrentMonth.AddDate(0, -1, 0)

	newMembersThisMonth, err := h.getNewMembersInPeriodSafe(ctx, gymIDStr, startOfCurrentMonth, now)
	if err != nil {
		log.Printf("Warning: Failed to fetch current month data: %v", err)
		newMembersThisMonth = 0 // Set to 0 instead of failing
	}

	newMembersLastMonth, err := h.getNewMembersInPeriodSafe(ctx, gymIDStr, startOfLastMonth, startOfCurrentMonth)
	if err != nil {
		log.Printf("Warning: Failed to fetch last month data: %v", err)
		newMembersLastMonth = 0 // Set to 0 instead of failing
	}

	monthlyGrowth, err := h.getMonthlyGrowthSafe(ctx, gymIDStr, 6)
	if err != nil {
		log.Printf("Warning: Failed to fetch monthly growth data: %v", err)
		monthlyGrowth = []models.MonthlyMemberCount{} // Empty array instead of failing
	}

	// Get class utilization stats
	classUtilization, err := h.getClassUtilizationStats(ctx, gymIDStr)
	if err != nil {
		log.Printf("Warning: Failed to fetch class utilization data: %v", err)
		classUtilization = gin.H{
			"classes":            []interface{}{},
			"overallUtilization": 0.0,
			"totalCapacity":      0,
			"totalUtilized":      0,
		}
	}

	// Compile all insights data
	insights := gin.H{
		"basicStats": gin.H{
			"totalMembers":  totalMembers,
			"activeMembers": activeMembers,
			"totalClasses":  totalClasses,
			"activeClasses": activeClasses,
		},
		"memberGrowth": gin.H{
			"newMembersThisMonth": newMembersThisMonth,
			"newMembersLastMonth": newMembersLastMonth,
			"monthlyGrowth":       monthlyGrowth,
		},
		"classUtilization": classUtilization,
		"metadata": gin.H{
			"generatedAt": now.Unix(),
			"gymId":       gymIDStr,
			"month":       now.Format("January 2006"),
		},
	}

	c.JSON(http.StatusOK, gin.H{"insights": insights})
}

// Legacy endpoints (kept for backward compatibility)
func (h *DashboardHandler) GetNewMembersThisMonth(c *gin.Context) {
	gymID, exists := c.Get("gymID")
	if !exists || gymID == "" {
		c.JSON(http.StatusForbidden, gin.H{"error": "No gym access"})
		return
	}

	ctx := c.Request.Context()
	now := time.Now()
	startOfMonth := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())

	iter := h.firebaseService.Collection("members").
		Where("gymId", "==", gymID).
		Where("joinDate", ">=", startOfMonth).
		Documents(ctx)

	var newMembersCount int
	for {
		_, err := iter.Next()
		if err == iterator.Done {
			break
		}
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch new members data"})
			return
		}
		newMembersCount++
	}

	c.JSON(http.StatusOK, gin.H{
		"newMembersThisMonth": newMembersCount,
		"month":               now.Format("January 2006"),
	})
}

func (h *DashboardHandler) GetMemberGrowthStats(c *gin.Context) {
	gymID, exists := c.Get("gymID")
	if !exists || gymID == "" {
		c.JSON(http.StatusForbidden, gin.H{"error": "No gym access"})
		return
	}

	ctx := c.Request.Context()
	now := time.Now()
	startOfCurrentMonth := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	startOfLastMonth := startOfCurrentMonth.AddDate(0, -1, 0)

	newMembersThisMonth, err := h.getNewMembersInPeriod(ctx, gymID.(string), startOfCurrentMonth, now)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch current month data"})
		return
	}

	newMembersLastMonth, err := h.getNewMembersInPeriod(ctx, gymID.(string), startOfLastMonth, startOfCurrentMonth)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch last month data"})
		return
	}

	monthlyGrowth, err := h.getMonthlyGrowth(ctx, gymID.(string), 6)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch monthly growth data"})
		return
	}

	stats := models.MemberGrowthStats{
		NewMembersThisMonth: newMembersThisMonth,
		NewMembersLastMonth: newMembersLastMonth,
		MonthlyGrowth:       monthlyGrowth,
	}

	c.JSON(http.StatusOK, gin.H{"memberGrowth": stats})
}

// Helper functions
func (h *DashboardHandler) getMemberCounts(ctx context.Context, gymID string) (int, int, error) {
	iter := h.firebaseService.Collection("members").Where("gymId", "==", gymID).Documents(ctx)

	var totalMembers, activeMembers int
	for {
		doc, err := iter.Next()
		if err == iterator.Done {
			break
		}
		if err != nil {
			return 0, 0, err
		}

		var member models.Member
		if err := doc.DataTo(&member); err != nil {
			continue
		}

		totalMembers++
		if member.IsActive {
			activeMembers++
		}
	}

	return totalMembers, activeMembers, nil
}

func (h *DashboardHandler) getClassCounts(ctx context.Context, gymID string) (int, int, error) {
	iter := h.firebaseService.Collection("classes").Where("gymId", "==", gymID).Documents(ctx)

	var totalClasses, activeClasses int
	for {
		doc, err := iter.Next()
		if err == iterator.Done {
			break
		}
		if err != nil {
			return 0, 0, err
		}

		var class models.WeeklyClass
		if err := doc.DataTo(&class); err != nil {
			continue
		}

		totalClasses++
		if class.IsActive {
			activeClasses++
		}
	}

	return totalClasses, activeClasses, nil
}

func (h *DashboardHandler) getNewMembersInPeriod(ctx context.Context, gymID string, startDate, endDate time.Time) (int, error) {
	// Debug: Log the query parameters
	log.Printf("Querying members for gymID: %s, startDate: %v, endDate: %v", gymID, startDate, endDate)

	iter := h.firebaseService.Collection("members").
		Where("gymId", "==", gymID).
		Where("joinDate", ">=", startDate).
		Where("joinDate", "<", endDate).
		Documents(ctx)

	var count int
	for {
		doc, err := iter.Next()
		if err == iterator.Done {
			break
		}
		if err != nil {
			log.Printf("Error iterating members: %v", err)
			return 0, err
		}

		// Debug: Log found member
		var member models.Member
		if err := doc.DataTo(&member); err != nil {
			log.Printf("Error parsing member data: %v", err)
			continue
		}
		log.Printf("Found member: %s %s, joinDate: %v", member.FirstName, member.LastName, member.JoinDate)
		count++
	}

	log.Printf("Total members found in period: %d", count)
	return count, nil
}

func (h *DashboardHandler) getMonthlyGrowth(ctx context.Context, gymID string, months int) ([]models.MonthlyMemberCount, error) {
	var monthlyGrowth []models.MonthlyMemberCount
	now := time.Now()

	for i := months - 1; i >= 0; i-- {
		targetMonth := now.AddDate(0, -i, 0)
		startOfMonth := time.Date(targetMonth.Year(), targetMonth.Month(), 1, 0, 0, 0, 0, targetMonth.Location())
		endOfMonth := startOfMonth.AddDate(0, 1, 0)

		count, err := h.getNewMembersInPeriod(ctx, gymID, startOfMonth, endOfMonth)
		if err != nil {
			return nil, err
		}

		monthlyGrowth = append(monthlyGrowth, models.MonthlyMemberCount{
			Month: startOfMonth.Format("2006-01"),
			Count: count,
		})
	}

	return monthlyGrowth, nil
}

// Safe versions of methods that handle errors gracefully
func (h *DashboardHandler) getNewMembersInPeriodSafe(ctx context.Context, gymID string, startDate, endDate time.Time) (int, error) {
	log.Printf("Querying members for gymID: %s, startDate: %v, endDate: %v", gymID, startDate, endDate)

	// First, try to get all members for this gym
	iter := h.firebaseService.Collection("members").Where("gymId", "==", gymID).Documents(ctx)

	var count int
	for {
		doc, err := iter.Next()
		if err == iterator.Done {
			break
		}
		if err != nil {
			log.Printf("Error iterating members: %v", err)
			return 0, err
		}

		var member models.Member
		if err := doc.DataTo(&member); err != nil {
			log.Printf("Error parsing member data: %v", err)
			continue
		}

		// Check if joinDate is within the period
		// Handle cases where joinDate might be zero value or nil
		if !member.JoinDate.IsZero() {
			if member.JoinDate.After(startDate) || member.JoinDate.Equal(startDate) {
				if member.JoinDate.Before(endDate) {
					log.Printf("Found member in period: %s %s, joinDate: %v", member.FirstName, member.LastName, member.JoinDate)
					count++
				}
			}
		} else {
			// If no joinDate, assume it's an older member (before current month)
			log.Printf("Member %s %s has no joinDate, skipping", member.FirstName, member.LastName)
		}
	}

	log.Printf("Total members found in period: %d", count)
	return count, nil
}

func (h *DashboardHandler) getMonthlyGrowthSafe(ctx context.Context, gymID string, months int) ([]models.MonthlyMemberCount, error) {
	var monthlyGrowth []models.MonthlyMemberCount
	now := time.Now()

	for i := months - 1; i >= 0; i-- {
		targetMonth := now.AddDate(0, -i, 0)
		startOfMonth := time.Date(targetMonth.Year(), targetMonth.Month(), 1, 0, 0, 0, 0, targetMonth.Location())
		endOfMonth := startOfMonth.AddDate(0, 1, 0)

		count, err := h.getNewMembersInPeriodSafe(ctx, gymID, startOfMonth, endOfMonth)
		if err != nil {
			log.Printf("Warning: Failed to get count for month %s: %v", startOfMonth.Format("2006-01"), err)
			count = 0 // Use 0 instead of failing
		}

		monthlyGrowth = append(monthlyGrowth, models.MonthlyMemberCount{
			Month: startOfMonth.Format("2006-01"),
			Count: count,
		})
	}

	return monthlyGrowth, nil
}

func (h *DashboardHandler) getClassUtilizationStats(ctx context.Context, gymID string) (gin.H, error) {
	iter := h.firebaseService.Collection("classes").
		Where("gymId", "==", gymID).
		Where("isActive", "==", true).
		Documents(ctx)

	type ClassUtilization struct {
		ClassName      string  `json:"className"`
		CurrentSize    int     `json:"currentSize"`
		MaxSize        int     `json:"maxSize"`
		UtilizationPct float64 `json:"utilizationPct"`
		Coach          string  `json:"coach"`
		DayOfWeek      int     `json:"dayOfWeek"`
	}

	var classStats []ClassUtilization
	var totalCapacity, totalUtilized int

	for {
		doc, err := iter.Next()
		if err == iterator.Done {
			break
		}
		if err != nil {
			return nil, err
		}

		var class models.WeeklyClass
		if err := doc.DataTo(&class); err != nil {
			continue
		}

		utilizationPct := 0.0
		if class.MaxSize > 0 {
			utilizationPct = (float64(class.CurrentSize) / float64(class.MaxSize)) * 100
		}

		classStats = append(classStats, ClassUtilization{
			ClassName:      class.Name,
			CurrentSize:    class.CurrentSize,
			MaxSize:        class.MaxSize,
			UtilizationPct: utilizationPct,
			Coach:          class.Coach,
			DayOfWeek:      class.DayOfWeek,
		})

		totalCapacity += class.MaxSize
		totalUtilized += class.CurrentSize
	}

	overallUtilization := 0.0
	if totalCapacity > 0 {
		overallUtilization = (float64(totalUtilized) / float64(totalCapacity)) * 100
	}

	return gin.H{
		"classes":            classStats,
		"overallUtilization": overallUtilization,
		"totalCapacity":      totalCapacity,
		"totalUtilized":      totalUtilized,
	}, nil
}
