package handlers

import (
	"net/http"
	"time"

	"dojofy/internal/models"
	"dojofy/internal/services"

	"cloud.google.com/go/firestore"
	"github.com/gin-gonic/gin"
	"google.golang.org/api/iterator"
)

// MembershipHandler handles membership-related requests
type MembershipHandler struct {
	firebaseService *services.FirebaseService
}

// NewMembershipHandler creates a new membership handler
func NewMembershipHandler(firebaseService *services.FirebaseService) *MembershipHandler {
	return &MembershipHandler{
		firebaseService: firebaseService,
	}
}

// GetAllMembershipPlans retrieves all membership plans for the authenticated user's gym
func (h *MembershipHandler) GetAllMembershipPlans(c *gin.Context) {
	gymID, exists := c.Get("gymID")
	if !exists || gymID == "" {
		c.JSON(http.StatusForbidden, gin.H{"error": "No gym access"})
		return
	}

	iter := h.firebaseService.Collection("membership_plans").Where("gymId", "==", gymID).Documents(c.Request.Context())

	var plans []models.MembershipPlan
	for {
		doc, err := iter.Next()
		if err == iterator.Done {
			break
		}
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch membership plans"})
			return
		}

		var plan models.MembershipPlan
		if err := doc.DataTo(&plan); err != nil {
			continue
		}
		plan.ID = doc.Ref.ID
		plans = append(plans, plan)
	}

	c.JSON(http.StatusOK, gin.H{"plans": plans})
}

// GetMembershipPlanByID retrieves a specific membership plan
func (h *MembershipHandler) GetMembershipPlanByID(c *gin.Context) {
	planID := c.Param("id")
	gymID, exists := c.Get("gymID")
	if !exists || gymID == "" {
		c.JSON(http.StatusForbidden, gin.H{"error": "No gym access"})
		return
	}

	doc, err := h.firebaseService.GetDocument(c.Request.Context(), "membership_plans", planID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Membership plan not found"})
		return
	}

	var plan models.MembershipPlan
	if err := doc.DataTo(&plan); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to parse plan data"})
		return
	}

	if plan.GymID != gymID {
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
		return
	}

	plan.ID = doc.Ref.ID
	c.JSON(http.StatusOK, gin.H{"plan": plan})
}

// CreateMembershipPlan creates a new membership plan
func (h *MembershipHandler) CreateMembershipPlan(c *gin.Context) {
	var request models.CreateMembershipPlanRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	gymID, exists := c.Get("gymID")
	if !exists || gymID == "" {
		c.JSON(http.StatusForbidden, gin.H{"error": "No gym access"})
		return
	}

	plan := models.MembershipPlan{
		Name:            request.Name,
		Description:     request.Description,
		Price:           request.Price,
		Currency:        request.Currency,
		BillingCycle:    request.BillingCycle,
		ClassesPerWeek:  request.ClassesPerWeek,
		IncludesPrivate: request.IncludesPrivate,
		IsActive:        true,
		GymID:           gymID.(string),
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	docRef, _, err := h.firebaseService.Collection("membership_plans").Add(c.Request.Context(), plan)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create membership plan"})
		return
	}

	plan.ID = docRef.ID
	c.JSON(http.StatusCreated, gin.H{"plan": plan})
}

// UpdateMembershipPlan updates an existing membership plan
func (h *MembershipHandler) UpdateMembershipPlan(c *gin.Context) {
	planID := c.Param("id")
	var request models.UpdateMembershipPlanRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	gymID, exists := c.Get("gymID")
	if !exists || gymID == "" {
		c.JSON(http.StatusForbidden, gin.H{"error": "No gym access"})
		return
	}

	// Verify plan exists and belongs to user's gym
	doc, err := h.firebaseService.GetDocument(c.Request.Context(), "membership_plans", planID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Membership plan not found"})
		return
	}

	var existingPlan models.MembershipPlan
	if err := doc.DataTo(&existingPlan); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to parse plan data"})
		return
	}

	if existingPlan.GymID != gymID {
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
		return
	}

	// Build update map
	updates := []firestore.Update{
		{Path: "updatedAt", Value: time.Now()},
	}

	if request.Name != "" {
		updates = append(updates, firestore.Update{Path: "name", Value: request.Name})
	}
	if request.Description != "" {
		updates = append(updates, firestore.Update{Path: "description", Value: request.Description})
	}
	if request.Price != nil {
		updates = append(updates, firestore.Update{Path: "price", Value: *request.Price})
	}
	if request.Currency != "" {
		updates = append(updates, firestore.Update{Path: "currency", Value: request.Currency})
	}
	if request.BillingCycle != "" {
		updates = append(updates, firestore.Update{Path: "billingCycle", Value: request.BillingCycle})
	}
	if request.ClassesPerWeek != nil {
		updates = append(updates, firestore.Update{Path: "classesPerWeek", Value: *request.ClassesPerWeek})
	}
	if request.IncludesPrivate != nil {
		updates = append(updates, firestore.Update{Path: "includesPrivate", Value: *request.IncludesPrivate})
	}
	if request.IsActive != nil {
		updates = append(updates, firestore.Update{Path: "isActive", Value: *request.IsActive})
	}

	// Update in Firestore
	if err := h.firebaseService.UpdateDocument(c.Request.Context(), "membership_plans", planID, updates); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update membership plan"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Membership plan updated successfully"})
}

// DeleteMembershipPlan deletes a membership plan
func (h *MembershipHandler) DeleteMembershipPlan(c *gin.Context) {
	planID := c.Param("id")
	gymID, exists := c.Get("gymID")
	if !exists || gymID == "" {
		c.JSON(http.StatusForbidden, gin.H{"error": "No gym access"})
		return
	}

	// Verify plan exists and belongs to user's gym
	doc, err := h.firebaseService.GetDocument(c.Request.Context(), "membership_plans", planID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Membership plan not found"})
		return
	}

	var plan models.MembershipPlan
	if err := doc.DataTo(&plan); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to parse plan data"})
		return
	}

	if plan.GymID != gymID {
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
		return
	}

	// Delete from Firestore
	if err := h.firebaseService.DeleteDocument(c.Request.Context(), "membership_plans", planID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete membership plan"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Membership plan deleted successfully"})
}
