package handlers

import (
	"encoding/json"
	"log"
	"net/http"
	"time"

	"cloud.google.com/go/firestore"
	"github.com/gin-gonic/gin"
	"github.com/stripe/stripe-go/v72"
	"github.com/stripe/stripe-go/v72/webhook"
	"google.golang.org/api/iterator"

	"dojofy/internal/models"
	"dojofy/internal/services"
)

// PaymentHandler handles payment-related requests
type PaymentHandler struct {
	firebaseService *services.FirebaseService
	stripeService   *services.StripeService
}

// NewPaymentHandler creates a new payment handler
func NewPaymentHandler(firebaseService *services.FirebaseService, stripeService *services.StripeService) *PaymentHandler {
	return &PaymentHandler{
		firebaseService: firebaseService,
		stripeService:   stripeService,
	}
}

// CreateCheckoutSession creates a Stripe checkout session for a membership
func (h *PaymentHandler) CreateCheckoutSession(c *gin.Context) {
	// Debug: Print all context keys
	log.Printf("DEBUG: All context keys: %+v", c.Keys)

	// Get user's gym ID from context
	gymID, exists := c.Get("gymID")
	if !exists {
		log.Printf("ERROR: GymID not found in context. Available context keys: %v", c.Keys)
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized - GymID not found in context"})
		return
	}

	log.Printf("DEBUG: Creating checkout session for gym: %v (type: %T)", gymID, gymID)

	// Parse request
	var request struct {
		MembershipPlanID string `json:"membershipPlanId" binding:"required"`
		MemberID         string `json:"memberId" binding:"required"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		log.Printf("ERROR: Failed to parse request: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	log.Printf("DEBUG: Request data: membershipPlanId=%s, memberId=%s", request.MembershipPlanID, request.MemberID)

	// Get membership plan
	planDoc, err := h.firebaseService.GetDocument(c.Request.Context(), "membership_plans", request.MembershipPlanID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Membership plan not found"})
		return
	}

	var plan models.MembershipPlan
	if err := planDoc.DataTo(&plan); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to parse plan data"})
		return
	}

	// Verify plan belongs to user's gym
	gymIDStr, ok := gymID.(string)
	if !ok {
		log.Printf("ERROR: GymID is not a string: %v (type: %T)", gymID, gymID)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid gym ID format"})
		return
	}

	log.Printf("DEBUG: Comparing plan.GymID (%s) with gymIDStr (%s)", plan.GymID, gymIDStr)
	if plan.GymID != gymIDStr {
		log.Printf("ERROR: Access denied - plan belongs to gym %s, user is from gym %s", plan.GymID, gymIDStr)
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied to membership plan"})
		return
	}

	// Get member
	memberDoc, err := h.firebaseService.GetDocument(c.Request.Context(), "members", request.MemberID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Member not found"})
		return
	}

	var member models.Member
	if err := memberDoc.DataTo(&member); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to parse member data"})
		return
	}

	// Create or get Stripe product and price if not already created
	if plan.StripeProductID == "" || plan.StripePriceID == "" {
		// Create product
		product, err := h.stripeService.CreateProduct(plan.Name, plan.Description)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create Stripe product"})
			return
		}
		plan.StripeProductID = product.ID

		// Convert billing cycle to Stripe interval
		interval := "month"
		if plan.BillingCycle == "quarterly" {
			interval = "quarter"
		} else if plan.BillingCycle == "yearly" {
			interval = "year"
		}

		// Create price (convert price to cents)
		priceAmount := int64(plan.Price * 100)
		price, err := h.stripeService.CreatePrice(product.ID, priceAmount, plan.Currency, interval)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create Stripe price"})
			return
		}
		plan.StripePriceID = price.ID

		// Update plan with Stripe IDs
		updates := []firestore.Update{
			{Path: "stripeProductId", Value: product.ID},
			{Path: "stripePriceId", Value: price.ID},
			{Path: "updatedAt", Value: time.Now()},
		}

		if err := h.firebaseService.UpdateDocument(c.Request.Context(), "membership_plans", request.MembershipPlanID, updates); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update membership plan"})
			return
		}
	}

	// Create or get Stripe customer
	customerID := member.StripeCustomerID
	if customerID == "" {
		customer, err := h.stripeService.CreateCustomer(member.Email, member.FirstName+" "+member.LastName)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create Stripe customer"})
			return
		}
		customerID = customer.ID

		// Update member with Stripe customer ID
		if err := h.firebaseService.UpdateDocument(c.Request.Context(), "members", request.MemberID, []firestore.Update{
			{Path: "stripeCustomerId", Value: customerID},
			{Path: "updatedAt", Value: time.Now()},
		}); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update member"})
			return
		}
	}

	// Create checkout session
	session, err := h.stripeService.CreateCheckoutSession(plan.StripePriceID, customerID, "subscription")
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create checkout session"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"sessionId": session.ID,
		"url":       session.URL,
	})
}

// HandleWebhook handles Stripe webhook events
func (h *PaymentHandler) HandleWebhook(c *gin.Context) {
	// Read the webhook body
	payload, err := c.GetRawData()
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to read request body"})
		return
	}

	// Get the signature from headers
	signature := c.GetHeader("Stripe-Signature")

	// Verify the webhook signature
	event, err := webhook.ConstructEvent(payload, signature, h.stripeService.GetWebhookSecret())
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid webhook signature"})
		return
	}

	// Handle different event types
	switch event.Type {
	case "checkout.session.completed":
		var session stripe.CheckoutSession
		err := json.Unmarshal(event.Data.Raw, &session)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to parse webhook data"})
			return
		}

		// Update member subscription status
		// Find member by customer ID
		iter := h.firebaseService.Collection("members").Where("stripeCustomerId", "==", session.Customer.ID).Documents(c.Request.Context())

		var members []*firestore.DocumentSnapshot
		for {
			doc, err := iter.Next()
			if err == iterator.Done {
				break
			}
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to query members"})
				return
			}
			members = append(members, doc)
		}

		if len(members) == 0 {
			c.JSON(http.StatusNotFound, gin.H{"error": "Member not found"})
			return
		}

		memberID := members[0].Ref.ID
		updates := []firestore.Update{
			{Path: "subscriptionId", Value: session.Subscription.ID},
			{Path: "paymentStatus", Value: "active"},
			{Path: "updatedAt", Value: time.Now()},
		}

		if err := h.firebaseService.UpdateDocument(c.Request.Context(), "members", memberID, updates); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update member"})
			return
		}

	case "invoice.payment_failed":
		var invoice stripe.Invoice
		err := json.Unmarshal(event.Data.Raw, &invoice)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to parse webhook data"})
			return
		}

		// Update member payment status
		iter := h.firebaseService.Collection("members").Where("stripeCustomerId", "==", invoice.Customer.ID).Documents(c.Request.Context())

		var members []*firestore.DocumentSnapshot
		for {
			doc, err := iter.Next()
			if err == iterator.Done {
				break
			}
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to query members"})
				return
			}
			members = append(members, doc)
		}

		if len(members) == 0 {
			c.JSON(http.StatusNotFound, gin.H{"error": "Member not found"})
			return
		}

		memberID := members[0].Ref.ID
		updates := []firestore.Update{
			{Path: "paymentStatus", Value: "past_due"},
			{Path: "updatedAt", Value: time.Now()},
		}

		if err := h.firebaseService.UpdateDocument(c.Request.Context(), "members", memberID, updates); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update member"})
			return
		}

	case "customer.subscription.deleted":
		var subscription stripe.Subscription
		err := json.Unmarshal(event.Data.Raw, &subscription)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to parse webhook data"})
			return
		}

		// Update member subscription status
		iter := h.firebaseService.Collection("members").Where("subscriptionId", "==", subscription.ID).Documents(c.Request.Context())

		var members []*firestore.DocumentSnapshot
		for {
			doc, err := iter.Next()
			if err == iterator.Done {
				break
			}
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to query members"})
				return
			}
			members = append(members, doc)
		}

		if len(members) == 0 {
			c.JSON(http.StatusNotFound, gin.H{"error": "Member not found"})
			return
		}

		memberID := members[0].Ref.ID
		updates := []firestore.Update{
			{Path: "paymentStatus", Value: "canceled"},
			{Path: "updatedAt", Value: time.Now()},
		}

		if err := h.firebaseService.UpdateDocument(c.Request.Context(), "members", memberID, updates); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update member"})
			return
		}
	}

	c.JSON(http.StatusOK, gin.H{"received": true})
}
