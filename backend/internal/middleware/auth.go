package middleware

import (
	"log"
	"net/http"
	"strings"

	"dojofy/internal/services"

	"github.com/gin-gonic/gin"
)

// AuthMiddleware validates Firebase ID tokens and adds user context to requests
func AuthMiddleware(firebaseService *services.FirebaseService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Extract token from Authorization header
		authHeader := c.<PERSON>eader("Authorization")
		if authHeader == "" {
			log.Printf("ERROR: Authorization header missing")
			c.<PERSON>SO<PERSON>(http.StatusUnauthorized, gin.H{
				"error": "Authorization header required",
			})
			c.Abort()
			return
		}

		// Check if header starts with "Bearer "
		tokenString := strings.TrimPrefix(authHeader, "Bearer ")
		if tokenString == authHeader {
			log.Printf("ERROR: Invalid authorization header format")
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Invalid authorization header format",
			})
			c.Abort()
			return
		}

		// Verify the Firebase ID token
		token, err := firebaseService.VerifyIDToken(c.Request.Context(), tokenString)
		if err != nil {
			log.Printf("ERROR: Token verification failed: %v", err)
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Invalid or expired token",
			})
			c.Abort()
			return
		}

		// Get user document from Firestore directly
		userFirestoreDoc, err := firebaseService.Collection("users").Doc(token.UID).Get(c.Request.Context())
		if err != nil {
			log.Printf("ERROR: Firestore user lookup failed: %v", err)
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "User not found in database",
			})
			c.Abort()
			return
		}

		var user struct {
			GymID    string `firestore:"gymId"` // Note the capitalization here
			Role     string `firestore:"role"`
			MemberID string `firestore:"memberId"`
		}
		if err := userFirestoreDoc.DataTo(&user); err != nil {
			log.Printf("ERROR: User data parsing failed: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to process user data",
			})
			c.Abort()
			return
		}

		// If no gym ID is found, redirect to auth endpoint
		if user.GymID == "" {
			log.Printf("ERROR: User %s has no gym ID", token.UID)
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "User account not fully set up",
				"code":  "INCOMPLETE_SETUP",
			})
			c.Abort()
			return
		}

		// Set user and gym ID in context
		c.Set("uid", token.UID)
		c.Set("gymID", user.GymID) // This is what's being set in the context
		c.Set("userRole", user.Role)
		c.Set("memberID", user.MemberID)

		log.Printf("DEBUG: Authenticated request: User %s, Gym %s, Role %s", token.UID, user.GymID, user.Role)
		log.Printf("DEBUG: Context after middleware: %+v", c.Keys)

		c.Next()
	}
}

// RequireGymAccess ensures the user has access to a specific gym
func RequireGymAccess() gin.HandlerFunc {
	return func(c *gin.Context) {
		gymID, exists := c.Get("gymID")
		if !exists || gymID == "" {
			c.JSON(http.StatusForbidden, gin.H{
				"error": "No gym access. Please contact your gym administrator.",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}
