package services

import (
	"fmt"

	"github.com/resendlabs/resend-go"
)

// EmailService handles sending emails
type EmailService struct {
	client *resend.Client
	from   string
}

// NewEmailService creates a new email service
func NewEmailService(apiKey string, fromEmail string) (*EmailService, error) {
	if apiKey == "" {
		return nil, fmt.Errorf("Resend API key is empty")
	}

	if fromEmail == "" {
		return nil, fmt.Errorf("From email is empty")
	}

	client := resend.NewClient(apiKey)

	return &EmailService{
		client: client,
		from:   fromEmail,
	}, nil
}

// SendMemberInvitation sends an invitation email to a member
func (es *EmailService) SendMemberInvitation(email, memberName, gymName, inviteLink string) error {
	params := &resend.SendEmailRequest{
		From:    es.from,
		To:      []string{email},
		Subject: fmt.Sprintf("Invitation to join %s on Dojofy", gymName),
		Html:    buildInvitationEmail(memberName, gymName, inviteLink),
	}

	_, err := es.client.Emails.Send(params)
	if err != nil {
		return err
	}

	return nil
}

// buildInvitationEmail creates the HTML content for the invitation email
func buildInvitationEmail(memberName, gymName, inviteLink string) string {
	return fmt.Sprintf(`
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Dojofy Invitation</title>
  <style>
    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
    .header { background-color: #4f46e5; color: white; padding: 20px; text-align: center; }
    .content { padding: 20px; }
    .button { display: inline-block; background-color: #4f46e5; color: white; text-decoration: none; padding: 10px 20px; border-radius: 5px; }
    .footer { text-align: center; margin-top: 20px; font-size: 12px; color: #666; }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Dojofy Invitation</h1>
    </div>
    <div class="content">
      <p>Hello %s,</p>
      <p>You've been invited to join <strong>%s</strong> on Dojofy, the martial arts gym management platform.</p>
      <p>Click the button below to create your account and access your membership information:</p>
      <p style="text-align: center; margin: 30px 0;">
        <a href="%s" class="button">Accept Invitation</a>
      </p>
      <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
      <p style="word-break: break-all;">%s</p>
      <p>This invitation will expire in 7 days.</p>
      <p>Best regards,<br>The Dojofy Team</p>
    </div>
    <div class="footer">
      <p>© 2025 Dojofy. All rights reserved.</p>
    </div>
  </div>
</body>
</html>
`, memberName, gymName, inviteLink, inviteLink)
}
