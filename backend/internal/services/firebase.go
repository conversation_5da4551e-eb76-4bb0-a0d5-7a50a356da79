package services

import (
	"context"
	"log"
	"os"

	"cloud.google.com/go/firestore"
	firebase "firebase.google.com/go/v4"
	"firebase.google.com/go/v4/auth"
	"google.golang.org/api/option"
)

// FirebaseService encapsulates Firebase operations
type FirebaseService struct {
	App       *firebase.App
	Auth      *auth.Client
	Firestore *firestore.Client
	ProjectID string
}

// NewFirebaseService creates and initializes a new Firebase service
func NewFirebaseService(projectID, credentialsPath string) (*FirebaseService, error) {
	ctx := context.Background()

	var app *firebase.App
	var err error

	useEmulators := os.Getenv("USE_FIREBASE_EMULATORS") == "true"

	if useEmulators {
		// Use emulators: no credentials required
		app, err = firebase.NewApp(ctx, &firebase.Config{
			ProjectID: projectID,
		}, option.WithoutAuthentication())
	} else if credentialsPath != "" {
		// Use service account key file
		opt := option.WithCredentialsFile(credentialsPath)
		app, err = firebase.NewApp(ctx, &firebase.Config{
			ProjectID: projectID,
		}, opt)
	} else {
		// Use default credentials (useful for Cloud Run, App Engine, etc.)
		app, err = firebase.NewApp(ctx, &firebase.Config{
			ProjectID: projectID,
		})
	}

	if err != nil {
		return nil, err
	}

	// Initialize Auth client
	authClient, err := app.Auth(ctx)
	if err != nil {
		return nil, err
	}

	// Initialize Firestore client
	firestoreClient, err := app.Firestore(ctx)
	if err != nil {
		return nil, err
	}

	log.Printf("Firebase service initialized successfully for project: %s", projectID)

	return &FirebaseService{
		App:       app,
		Auth:      authClient,
		Firestore: firestoreClient,
		ProjectID: projectID,
	}, nil
}

// Close closes the Firebase service connections
func (fs *FirebaseService) Close() error {
	if fs.Firestore != nil {
		return fs.Firestore.Close()
	}
	return nil
}

// VerifyIDToken verifies a Firebase ID token and returns the token claims
func (fs *FirebaseService) VerifyIDToken(ctx context.Context, idToken string) (*auth.Token, error) {
	token, err := fs.Auth.VerifyIDToken(ctx, idToken)
	if err != nil {
		return nil, err
	}
	return token, nil
}

// GetUserByUID retrieves user information by UID
func (fs *FirebaseService) GetUserByUID(ctx context.Context, uid string) (*auth.UserRecord, error) {
	user, err := fs.Auth.GetUser(ctx, uid)
	if err != nil {
		return nil, err
	}
	return user, nil
}

// Collection returns a Firestore collection reference
func (fs *FirebaseService) Collection(name string) *firestore.CollectionRef {
	return fs.Firestore.Collection(name)
}

// Add adds a new document to a collection with auto-generated ID
func (fs *FirebaseService) Add(ctx context.Context, collection string, data interface{}) (*firestore.DocumentRef, *firestore.WriteResult, error) {
	return fs.Firestore.Collection(collection).Add(ctx, data)
}

// CreateDocument creates a new document in the specified collection
func (fs *FirebaseService) CreateDocument(ctx context.Context, collection string, data interface{}) (*firestore.DocumentRef, error) {
	docRef := fs.Firestore.Collection(collection).NewDoc()
	_, err := docRef.Set(ctx, data)
	return docRef, err
}

// GetDocument retrieves a document by collection and document ID
func (fs *FirebaseService) GetDocument(ctx context.Context, collection, docID string) (*firestore.DocumentSnapshot, error) {
	return fs.Firestore.Collection(collection).Doc(docID).Get(ctx)
}

// UpdateDocument updates a document with the provided data
func (fs *FirebaseService) UpdateDocument(ctx context.Context, collection, docID string, updates []firestore.Update) error {
	_, err := fs.Firestore.Collection(collection).Doc(docID).Update(ctx, updates)
	return err
}

// DeleteDocument deletes a document from the specified collection
func (fs *FirebaseService) DeleteDocument(ctx context.Context, collection, docID string) error {
	_, err := fs.Firestore.Collection(collection).Doc(docID).Delete(ctx)
	return err
}

// RunTransaction executes a function within a Firestore transaction
func (fs *FirebaseService) RunTransaction(ctx context.Context, fn func(context.Context, *firestore.Transaction) error) error {
	return fs.Firestore.RunTransaction(ctx, fn)
}

// Batch returns a new Firestore batch
func (fs *FirebaseService) Batch() *firestore.WriteBatch {
	return fs.Firestore.Batch()
}

// ServerTimestamp returns a server timestamp sentinel value for Firestore
func (fs *FirebaseService) ServerTimestamp() interface{} {
	return firestore.ServerTimestamp
}

// MergeAll returns a merge option for Firestore
func (fs *FirebaseService) MergeAll() firestore.SetOption {
	return firestore.MergeAll
}
