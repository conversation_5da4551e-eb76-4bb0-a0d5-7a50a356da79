package services

import (
	"dojofy/internal/config"

	"github.com/stripe/stripe-go/v72"
	"github.com/stripe/stripe-go/v72/checkout/session"
	"github.com/stripe/stripe-go/v72/customer"
	"github.com/stripe/stripe-go/v72/price"
	"github.com/stripe/stripe-go/v72/product"
)

// StripeService handles interactions with the Stripe API
type StripeService struct {
	apiKey        string
	successURL    string
	cancelURL     string
	webhookSecret string
}

// NewStripeService creates a new Stripe service
func NewStripeService() *StripeService {
	cfg := config.Load()

	// Initialize Stripe
	stripe.Key = cfg.StripeSecretKey

	return &StripeService{
		apiKey:        cfg.StripeSecretKey,
		successURL:    cfg.StripeSuccessURL,
		cancelURL:     cfg.StripeCancelURL,
		webhookSecret: cfg.StripeWebhookSecret,
	}
}

// GetWebhookSecret returns the webhook secret
func (s *StripeService) GetWebhookSecret() string {
	return s.webhookSecret
}

// CreateProduct creates a Stripe product for a membership plan
func (s *StripeService) CreateProduct(name, description string) (*stripe.Product, error) {
	params := &stripe.ProductParams{
		Name:        stripe.String(name),
		Description: stripe.String(description),
	}

	return product.New(params)
}

// CreatePrice creates a Stripe price for a product
func (s *StripeService) CreatePrice(productID string, amount int64, currency string, interval string) (*stripe.Price, error) {
	params := &stripe.PriceParams{
		Product:    stripe.String(productID),
		UnitAmount: stripe.Int64(amount),
		Currency:   stripe.String(currency),
		Recurring: &stripe.PriceRecurringParams{
			Interval: stripe.String(interval),
		},
	}

	return price.New(params)
}

// CreateCustomer creates a Stripe customer
func (s *StripeService) CreateCustomer(email, name string) (*stripe.Customer, error) {
	params := &stripe.CustomerParams{
		Email: stripe.String(email),
		Name:  stripe.String(name),
	}

	return customer.New(params)
}

// CreateCheckoutSession creates a Stripe checkout session for a membership
func (s *StripeService) CreateCheckoutSession(priceID, customerID string, mode string) (*stripe.CheckoutSession, error) {
	params := &stripe.CheckoutSessionParams{
		SuccessURL: stripe.String(s.successURL),
		CancelURL:  stripe.String(s.cancelURL),
		Mode:       stripe.String(mode),
		LineItems: []*stripe.CheckoutSessionLineItemParams{
			{
				Price:    stripe.String(priceID),
				Quantity: stripe.Int64(1),
			},
		},
	}

	if customerID != "" {
		params.Customer = stripe.String(customerID)
	}

	return session.New(params)
}
