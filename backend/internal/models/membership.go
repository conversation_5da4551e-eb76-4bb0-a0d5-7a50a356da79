package models

import (
	"time"
)

// MembershipPlan represents a membership plan that can be offered to members
type MembershipPlan struct {
	ID              string    `json:"id" firestore:"id"`
	Name            string    `json:"name" firestore:"name" binding:"required"`
	Description     string    `json:"description" firestore:"description"`
	Price           float64   `json:"price" firestore:"price" binding:"required"`
	Currency        string    `json:"currency" firestore:"currency" binding:"required"`
	BillingCycle    string    `json:"billingCycle" firestore:"billingCycle" binding:"required"` // monthly, quarterly, yearly
	ClassesPerWeek  int       `json:"classesPerWeek" firestore:"classesPerWeek"`                // 0 means unlimited
	IncludesPrivate bool      `json:"includesPrivate" firestore:"includesPrivate"`
	IsActive        bool      `json:"isActive" firestore:"isActive"`
	GymID           string    `json:"gymId" firestore:"gymId"`
	CreatedAt       time.Time `json:"createdAt" firestore:"createdAt"`
	UpdatedAt       time.Time `json:"updatedAt" firestore:"updatedAt"`
	// Stripe integration fields
	StripeProductID string `json:"stripeProductId" firestore:"stripeProductId"`
	StripePriceID   string `json:"stripePriceId" firestore:"stripePriceId"`
}

// CreateMembershipPlanRequest represents the request payload for creating a membership plan
type CreateMembershipPlanRequest struct {
	Name            string  `json:"name" binding:"required"`
	Description     string  `json:"description"`
	Price           float64 `json:"price" binding:"required"`
	Currency        string  `json:"currency" binding:"required"`
	BillingCycle    string  `json:"billingCycle" binding:"required"`
	ClassesPerWeek  int     `json:"classesPerWeek"`
	IncludesPrivate bool    `json:"includesPrivate"`
}

// UpdateMembershipPlanRequest represents the request payload for updating a membership plan
type UpdateMembershipPlanRequest struct {
	Name            string   `json:"name"`
	Description     string   `json:"description"`
	Price           *float64 `json:"price"`
	Currency        string   `json:"currency"`
	BillingCycle    string   `json:"billingCycle"`
	ClassesPerWeek  *int     `json:"classesPerWeek"`
	IncludesPrivate *bool    `json:"includesPrivate"`
	IsActive        *bool    `json:"isActive"`
}
