package models

import (
	"time"
)

// User represents a user in the system
type User struct {
	UID       string    `json:"uid" firestore:"uid"`
	Email     string    `json:"email" firestore:"email"`
	Name      string    `json:"name" firestore:"name"`
	GymID     string    `json:"gymId" firestore:"gymId"`
	Role      string    `json:"role" firestore:"role"`
	CreatedAt time.Time `json:"createdAt" firestore:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt" firestore:"updatedAt"`
}
