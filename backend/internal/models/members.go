// models/member.go
package models

import (
	"time"
)

// Member represents a gym member
type Member struct {
	ID               string    `json:"id" firestore:"id"`
	FirstName        string    `json:"firstName" firestore:"firstName" binding:"required"`
	LastName         string    `json:"lastName" firestore:"lastName" binding:"required"`
	Email            string    `json:"email" firestore:"email" binding:"required,email"`
	Phone            string    `json:"phone" firestore:"phone"`
	PaymentMethod    string    `json:"paymentMethod" firestore:"paymentMethod" binding:"required"`
	MartialArt       string    `json:"martialArt" firestore:"martialArt" binding:"required"`
	MembershipPlanID string    `json:"membershipPlanId" firestore:"membershipPlanId" binding:"required"`
	ProfilePicURL    string    `json:"profilePicUrl" firestore:"profilePicUrl"`
	JoinDate         time.Time `json:"joinDate" firestore:"joinDate"`
	IsActive         bool      `json:"isActive" firestore:"isActive"`
	GymID            string    `json:"gymId" firestore:"gymId"`
	UserUID          string    `json:"userUid,omitempty" firestore:"userUid,omitempty"`
	CreatedAt        time.Time `json:"createdAt" firestore:"createdAt"`
	UpdatedAt        time.Time `json:"updatedAt" firestore:"updatedAt"`
}

// CreateMemberRequest represents the request payload for creating a member
type CreateMemberRequest struct {
	FirstName        string `json:"firstName" binding:"required"`
	LastName         string `json:"lastName" binding:"required"`
	Email            string `json:"email" binding:"required,email"`
	Phone            string `json:"phone"`
	PaymentMethod    string `json:"paymentMethod" binding:"required"`
	MartialArt       string `json:"martialArt" binding:"required"`
	MembershipPlanID string `json:"membershipPlanId" binding:"required"`
}

// UpdateMemberRequest represents the request payload for updating a member
type UpdateMemberRequest struct {
	FirstName        string `json:"firstName"`
	LastName         string `json:"lastName"`
	Email            string `json:"email"`
	Phone            string `json:"phone"`
	PaymentMethod    string `json:"paymentMethod"`
	MartialArt       string `json:"martialArt"`
	MembershipPlanID string `json:"membershipPlanId"`
	IsActive         *bool  `json:"isActive"`
}
