// models/gym.go
package models

import (
	"time"
)

// Gym represents a martial arts gym
type Gym struct {
	ID        string    `json:"id" firestore:"id"`
	Name      string    `json:"name" firestore:"name" binding:"required"`
	Address   string    `json:"address" firestore:"address"`
	Phone     string    `json:"phone" firestore:"phone"`
	Email     string    `json:"email" firestore:"email"`
	OwnerUID  string    `json:"ownerUid" firestore:"ownerUid"` // Firebase Auth UID of the owner
	Owners    []string  `json:"owners" firestore:"owners"`     // List of Firebase Auth UIDs who can manage this gym
	CreatedAt time.Time `json:"createdAt" firestore:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt" firestore:"updatedAt"`
	IsActive  bool      `json:"isActive" firestore:"isActive"`
}

// DashboardStats represents basic statistics for the dashboard
type DashboardStats struct {
	TotalMembers  int `json:"totalMembers"`
	ActiveMembers int `json:"activeMembers"`
	TotalClasses  int `json:"totalClasses"`
	ActiveClasses int `json:"activeClasses"`
}

// MemberGrowthStats represents member growth statistics over time
type MemberGrowthStats struct {
	NewMembersThisMonth int                  `json:"newMembersThisMonth"`
	NewMembersLastMonth int                  `json:"newMembersLastMonth"`
	MonthlyGrowth       []MonthlyMemberCount `json:"monthlyGrowth"`
}

// MonthlyMemberCount represents member count for a specific month
type MonthlyMemberCount struct {
	Month string `json:"month"` // Format: "2024-01"
	Count int    `json:"count"`
}
