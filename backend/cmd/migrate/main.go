package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"dojofy/internal/services"

	"cloud.google.com/go/firestore"
	"google.golang.org/api/iterator"
)

func main() {
	ctx := context.Background()

	firebaseService, err := services.NewFirebaseService("dojofy-dev", "")
	if err != nil {
		log.Fatalf("Failed to initialize Firebase service: %v", err)
	}
	defer firebaseService.Close()

	log.Println("Starting migration to fix class_instances with string time fields...")

	// Get all class instances
	iter := firebaseService.Collection("class_instances").Documents(ctx)

	var fixedCount int
	var totalCount int

	for {
		doc, err := iter.Next()
		if err == iterator.Done {
			break
		}
		if err != nil {
			log.Printf("Error iterating documents: %v", err)
			continue
		}

		totalCount++
		data := doc.Data()

		// Check if startTime is a string and fix it
		if startTimeStr, ok := data["startTime"].(string); ok {
			log.Printf("Found string startTime in document %s: %s", doc.Ref.ID, startTimeStr)

			parsedTime, err := parseTimeString(startTimeStr, data)
			if err != nil {
				log.Printf("Failed to parse startTime: %v", err)
				continue
			}

			// Update this document immediately
			err = firebaseService.UpdateDocument(ctx, "class_instances", doc.Ref.ID, []firestore.Update{
				{Path: "startTime", Value: parsedTime},
			})
			if err != nil {
				log.Printf("Failed to update document %s: %v", doc.Ref.ID, err)
				continue
			}
			fixedCount++
			log.Printf("Fixed startTime in document %s", doc.Ref.ID)
		}

		// Check if endTime is a string and fix it
		if endTimeStr, ok := data["endTime"].(string); ok {
			log.Printf("Found string endTime in document %s: %s", doc.Ref.ID, endTimeStr)

			parsedTime, err := parseTimeString(endTimeStr, data)
			if err != nil {
				log.Printf("Failed to parse endTime: %v", err)
				continue
			}

			// Update this document immediately
			err = firebaseService.UpdateDocument(ctx, "class_instances", doc.Ref.ID, []firestore.Update{
				{Path: "endTime", Value: parsedTime},
			})
			if err != nil {
				log.Printf("Failed to update document %s: %v", doc.Ref.ID, err)
				continue
			}
			fixedCount++
			log.Printf("Fixed endTime in document %s", doc.Ref.ID)
		}
	}

	log.Printf("Migration completed! Processed %d documents, fixed %d", totalCount, fixedCount)
}

func parseTimeString(timeStr string, data map[string]interface{}) (time.Time, error) {
	// Try to parse as RFC3339 (ISO8601) - full datetime
	if parsedTime, err := time.Parse(time.RFC3339, timeStr); err == nil {
		return parsedTime, nil
	}

	// Try to parse as "2006-01-02T15:04:05Z07:00" format
	if parsedTime, err := time.Parse("2006-01-02T15:04:05Z07:00", timeStr); err == nil {
		return parsedTime, nil
	}

	// Try to parse as "2006-01-02 15:04:05" format
	if parsedTime, err := time.Parse("2006-01-02 15:04:05", timeStr); err == nil {
		return parsedTime, nil
	}

	// Try to parse as time-only "15:04" format
	if parsedTime, err := time.Parse("15:04", timeStr); err == nil {
		// If it's just a time, combine it with the date from the class instance
		if dateStr, ok := data["date"].(string); ok {
			date, err := time.Parse("2006-01-02", dateStr)
			if err != nil {
				return time.Time{}, err
			}

			return time.Date(
				date.Year(), date.Month(), date.Day(),
				parsedTime.Hour(), parsedTime.Minute(), 0, 0,
				date.Location(),
			), nil
		}
		return time.Time{}, fmt.Errorf("no date field found for time-only string")
	}

	return time.Time{}, fmt.Errorf("could not parse time string: %s", timeStr)
}
