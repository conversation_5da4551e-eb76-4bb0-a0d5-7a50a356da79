package main

import (
	"context"
	"log"
	"time"

	"dojofy/internal/services"

	"google.golang.org/api/iterator"
)

func main() {
	ctx := context.Background()

	firebaseService, err := services.NewFirebaseService("dojofy-dev", "")
	if err != nil {
		log.Fatalf("Failed to initialize Firebase service: %v", err)
	}
	defer firebaseService.Close()

	log.Println("Diagnostic: Checking all class_instances documents...")

	// Get all class instances
	iter := firebaseService.Collection("class_instances").Documents(ctx)

	var totalCount int
	var pastCount int
	var futureCount int
	var currentCount int

	for {
		doc, err := iter.Next()
		if err == iterator.Done {
			break
		}
		if err != nil {
			log.Printf("Error iterating documents: %v", err)
			continue
		}

		totalCount++
		data := doc.Data()

		// Check date field
		if dateStr, ok := data["date"].(string); ok {
			date, err := time.Parse("2006-01-02", dateStr)
			if err != nil {
				log.Printf("Document %s: Invalid date format '%s': %v", doc.Ref.ID, dateStr, err)
				continue
			}

			// Compare with today
			today := time.Now().Truncate(24 * time.Hour)
			docDate := date.Truncate(24 * time.Hour)

			if docDate.Before(today) {
				pastCount++
				log.Printf("Document %s: PAST date %s", doc.Ref.ID, dateStr)
			} else if docDate.Equal(today) {
				currentCount++
				log.Printf("Document %s: TODAY %s", doc.Ref.ID, dateStr)
			} else {
				futureCount++
				log.Printf("Document %s: FUTURE date %s", doc.Ref.ID, dateStr)
			}
		} else {
			log.Printf("Document %s: No date field found", doc.Ref.ID)
		}

		// Check time field types
		if startTime, exists := data["startTime"]; exists {
			switch v := startTime.(type) {
			case string:
				log.Printf("Document %s: startTime is STRING: %s", doc.Ref.ID, v)
			case time.Time:
				log.Printf("Document %s: startTime is TIMESTAMP: %v", doc.Ref.ID, v)
			default:
				log.Printf("Document %s: startTime is UNKNOWN TYPE: %T", doc.Ref.ID, v)
			}
		}

		if endTime, exists := data["endTime"]; exists {
			switch v := endTime.(type) {
			case string:
				log.Printf("Document %s: endTime is STRING: %s", doc.Ref.ID, v)
			case time.Time:
				log.Printf("Document %s: endTime is TIMESTAMP: %v", doc.Ref.ID, v)
			default:
				log.Printf("Document %s: endTime is UNKNOWN TYPE: %T", doc.Ref.ID, v)
			}
		}
	}

	log.Printf("\n=== DIAGNOSTIC SUMMARY ===")
	log.Printf("Total documents in collection: %d", totalCount)
	log.Printf("Past documents: %d", pastCount)
	log.Printf("Today's documents: %d", currentCount)
	log.Printf("Future documents: %d", futureCount)
	log.Printf("==========================")
}
