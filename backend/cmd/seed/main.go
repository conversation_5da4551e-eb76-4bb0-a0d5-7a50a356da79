package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"time"

	"dojofy/internal/config"
	"dojofy/internal/models"
	"dojofy/internal/services"

	"github.com/joho/godotenv"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using system environment variables")
	}

	// Set emulator environment
	os.Setenv("USE_FIREBASE_EMULATORS", "true")
	os.Setenv("FIREBASE_PROJECT_ID", "demo-project")
	os.Setenv("FIRESTORE_EMULATOR_HOST", "localhost:8081")
	os.Setenv("FIREBASE_AUTH_EMULATOR_HOST", "localhost:9099")

	// Initialize configuration
	cfg := config.Load()

	// Initialize Firebase service
	firebaseService, err := services.NewFirebaseService(cfg.FirebaseProjectID, cfg.GoogleApplicationCredentials)
	if err != nil {
		log.Fatalf("Failed to initialize Firebase service: %v", err)
	}
	defer firebaseService.Close()

	ctx := context.Background()

	log.Println("Starting to seed Firebase emulator...")

	// Seed data
	if err := seedData(ctx, firebaseService); err != nil {
		log.Fatalf("Failed to seed data: %v", err)
	}

	log.Println("Seeding completed successfully!")
}

func seedData(ctx context.Context, firebaseService *services.FirebaseService) error {
	// Create test gym
	gymID, err := createTestGym(ctx, firebaseService)
	if err != nil {
		return err
	}

	// Create test users
	userIDs, err := createTestUsers(ctx, firebaseService)
	if err != nil {
		return err
	}

	// Create test classes
	classIDs, err := createTestClasses(ctx, firebaseService, gymID, userIDs)
	if err != nil {
		return err
	}

	// Create test members
	err = createTestMembers(ctx, firebaseService, gymID)
	if err != nil {
		return err
	}

	// Create test memberships
	err = createTestMemberships(ctx, firebaseService, gymID)
	if err != nil {
		return err
	}

	// Generate class instances
	err = generateClassInstances(ctx, firebaseService, classIDs)
	if err != nil {
		log.Printf("Failed to generate class instances: %v", err)
		return err
	}

	return nil
}

func createTestGym(ctx context.Context, firebaseService *services.FirebaseService) (string, error) {
	gym := models.Gym{
		Name:      "DojoFy Test Gym",
		Address:   "123 Martial Arts Way, Test City, TC 12345",
		Phone:     "******-0123",
		Email:     "<EMAIL>",
		IsActive:  true,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	docRef, err := firebaseService.CreateDocument(ctx, "gyms", gym)
	if err != nil {
		return "", err
	}

	log.Printf("Created test gym with ID: %s", docRef.ID)
	return docRef.ID, nil
}

func createTestUsers(ctx context.Context, firebaseService *services.FirebaseService) ([]string, error) {
	users := []models.User{
		{
			UID:       "test-user-1",
			Email:     "<EMAIL>",
			Role:      "coach",
			GymID:     "demo-gym",
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		{
			UID:       "test-user-2",
			Email:     "<EMAIL>",
			Role:      "admin",
			GymID:     "demo-gym",
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
	}

	var userIDs []string
	for _, user := range users {
		docRef, err := firebaseService.CreateDocument(ctx, "users", user)
		if err != nil {
			return nil, err
		}
		userIDs = append(userIDs, docRef.ID)
		log.Printf("Created test user: %s (%s)", user.UID, docRef.ID)
	}

	return userIDs, nil
}

func createTestClasses(ctx context.Context, firebaseService *services.FirebaseService, gymID string, userIDs []string) ([]string, error) {
	now := time.Now()
	startDate := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())

	classes := []models.WeeklyClass{
		{
			Name:        "Beginner Karate",
			Type:        "Karate",
			DayOfWeek:   1, // Monday
			StartTime:   "18:00",
			EndTime:     "19:00",
			Coach:       "Coach John",
			MaxSize:     20,
			CurrentSize: 0,
			StartDate:   startDate,
			IsActive:    true,
			GymID:       gymID,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			Name:        "Advanced BJJ",
			Type:        "Brazilian Jiu-Jitsu",
			DayOfWeek:   2, // Tuesday
			StartTime:   "19:00",
			EndTime:     "20:30",
			Coach:       "Coach John",
			MaxSize:     15,
			CurrentSize: 0,
			StartDate:   startDate,
			IsActive:    true,
			GymID:       gymID,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			Name:        "Muay Thai Basics",
			Type:        "Muay Thai",
			DayOfWeek:   3, // Wednesday
			StartTime:   "17:30",
			EndTime:     "18:30",
			Coach:       "Coach John",
			MaxSize:     25,
			CurrentSize: 0,
			StartDate:   startDate,
			IsActive:    true,
			GymID:       gymID,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			Name:        "Mixed Martial Arts",
			Type:        "MMA",
			DayOfWeek:   4, // Thursday
			StartTime:   "19:30",
			EndTime:     "21:00",
			Coach:       "Coach John",
			MaxSize:     18,
			CurrentSize: 0,
			StartDate:   startDate,
			IsActive:    true,
			GymID:       gymID,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			Name:        "Weekend Workshop",
			Type:        "Workshop",
			DayOfWeek:   6, // Saturday
			StartTime:   "10:00",
			EndTime:     "12:00",
			Coach:       "Coach John",
			MaxSize:     30,
			CurrentSize: 0,
			StartDate:   startDate,
			IsActive:    true,
			GymID:       gymID,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
	}

	var classIDs []string
	for _, class := range classes {
		docRef, err := firebaseService.CreateDocument(ctx, "weekly_classes", class)
		if err != nil {
			return nil, fmt.Errorf("failed to create class: %w", err)
		}
		classIDs = append(classIDs, docRef.ID)
		log.Printf("Created test class: %s (%s)", class.Name, docRef.ID)
	}

	return classIDs, nil
}

func createTestMembers(ctx context.Context, firebaseService *services.FirebaseService, gymID string) error {
	members := []models.Member{
		{
			FirstName:        "Alice",
			LastName:         "Johnson",
			Email:            "<EMAIL>",
			Phone:            "******-0101",
			MembershipPlanID: "basic-plan",
			JoinDate:         time.Now().AddDate(0, -2, 0),
			IsActive:         true,
			GymID:            gymID,
			CreatedAt:        time.Now(),
			UpdatedAt:        time.Now(),
		},
		{
			FirstName:        "Charlie",
			LastName:         "Brown",
			Email:            "<EMAIL>",
			Phone:            "******-0103",
			MembershipPlanID: "premium-plan",
			JoinDate:         time.Now().AddDate(0, -1, 0),
			IsActive:         true,
			GymID:            gymID,
			CreatedAt:        time.Now(),
			UpdatedAt:        time.Now(),
		},
		{
			FirstName:        "Eva",
			LastName:         "Martinez",
			Email:            "<EMAIL>",
			Phone:            "******-0105",
			MembershipPlanID: "basic-plan",
			JoinDate:         time.Now().AddDate(0, 0, -15),
			IsActive:         true,
			GymID:            gymID,
			CreatedAt:        time.Now(),
			UpdatedAt:        time.Now(),
		},
	}

	for _, member := range members {
		docRef, err := firebaseService.CreateDocument(ctx, "members", member)
		if err != nil {
			return err
		}
		log.Printf("Created test member: %s %s (%s)", member.FirstName, member.LastName, docRef.ID)
	}

	return nil
}

func createTestMemberships(ctx context.Context, firebaseService *services.FirebaseService, gymID string) error {
	memberships := []models.MembershipPlan{
		{
			Name:           "Basic Plan",
			Description:    "Access to basic classes and facilities",
			Price:          49.99,
			IsActive:       true,
			GymID:          gymID,
			CreatedAt:      time.Now(),
			UpdatedAt:      time.Now(),
			Currency:       "GBP",
			BillingCycle:   "monthly",
			ClassesPerWeek: 2,
		},
		{
			Name:           "Premium Plan",
			Description:    "Unlimited access to all classes and facilities",
			Price:          89.99,
			IsActive:       true,
			GymID:          gymID,
			CreatedAt:      time.Now(),
			UpdatedAt:      time.Now(),
			Currency:       "GBP",
			BillingCycle:   "monthly",
			ClassesPerWeek: 0,
		},
	}

	for _, membership := range memberships {
		docRef, err := firebaseService.CreateDocument(ctx, "membership_plans", membership)
		if err != nil {
			return err
		}
		log.Printf("Created test membership: %s (%s)", membership.Name, docRef.ID)
	}

	return nil
}

func generateClassInstances(ctx context.Context, firebaseService *services.FirebaseService, classIDs []string) error {
	// Get all weekly classes
	classesRef := firebaseService.Collection("weekly_classes")
	docs, err := classesRef.Documents(ctx).GetAll()
	if err != nil {
		return err
	}

	now := time.Now()
	endDate := now.AddDate(0, 0, 30) // Generate instances for next 30 days

	batch := firebaseService.Batch()
	instancesCollection := firebaseService.Collection("class_instances")
	generatedCount := 0

	for _, doc := range docs {
		var class models.WeeklyClass
		if err := doc.DataTo(&class); err != nil {
			continue
		}

		// Skip if template start date is in the future
		if class.StartDate.After(endDate) {
			continue
		}

		// Calculate first instance date based on day of week
		currentDate := now
		daysUntilNext := (class.DayOfWeek - int(currentDate.Weekday()) + 7) % 7
		if daysUntilNext == 0 && currentDate.Hour() >= 23 {
			daysUntilNext = 7
		}
		currentDate = currentDate.AddDate(0, 0, daysUntilNext)

		// Generate instances until end date
		for currentDate.Before(endDate) {
			// Check if instance already exists
			existingQuery := instancesCollection.
				Where("templateId", "==", doc.Ref.ID).
				Where("date", "==", currentDate.Format("2006-01-02")).
				Limit(1).
				Documents(ctx)

			_, err := existingQuery.Next()
			if err == nil {
				// Instance already exists, skip
				currentDate = currentDate.AddDate(0, 0, 7)
				continue
			}

			// Parse start and end times
			startTime, _ := time.Parse("15:04", class.StartTime)
			endTime, _ := time.Parse("15:04", class.EndTime)

			startTime = time.Date(currentDate.Year(), currentDate.Month(), currentDate.Day(), startTime.Hour(), startTime.Minute(), 0, 0, currentDate.Location())
			endTime = time.Date(currentDate.Year(), currentDate.Month(), currentDate.Day(), endTime.Hour(), endTime.Minute(), 0, 0, currentDate.Location())

			// Create class instance
			instance := models.ClassInstance{
				TemplateID:    doc.Ref.ID,
				GymID:         class.GymID,
				Name:          class.Name,
				Type:          class.Type,
				Date:          currentDate.Format("2006-01-02"),
				StartTime:     startTime,
				EndTime:       endTime,
				Coach:         class.Coach,
				MaxCapacity:   class.MaxSize,
				EnrolledCount: 0,
				WaitlistCount: 0,
				Status:        "scheduled",
				CreatedAt:     time.Now(),
			}

			// Add to batch
			newRef := instancesCollection.NewDoc()
			batch.Set(newRef, instance)
			generatedCount++

			// Move to next week
			currentDate = currentDate.AddDate(0, 0, 7)
		}
	}

	// Commit batch if there are instances to create
	if generatedCount > 0 {
		_, err := batch.Commit(ctx)
		if err != nil {
			return err
		}
		log.Printf("Generated %d class instances", generatedCount)
	}

	return nil
}
