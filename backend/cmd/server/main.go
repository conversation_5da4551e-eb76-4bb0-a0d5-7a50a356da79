package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"dojofy/internal/config"
	"dojofy/internal/handlers"
	"dojofy/internal/middleware"
	"dojofy/internal/services"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using system environment variables")
	}

	// Initialize configuration
	cfg := config.Load()

	// Debug configuration
	fmt.Println("\n==== CONFIGURATION ====")
	fmt.Printf("Port: %s\n", cfg.Port)
	fmt.Printf("Environment: %s\n", cfg.Environment)
	fmt.Printf("Firebase Project ID: %s\n", cfg.FirebaseProjectID)
	fmt.Printf("CORS Origin: %s\n", cfg.CORSOrigin)
	fmt.Printf("Resend API Key set: %v\n", cfg.ResendAPIKey != "")
	fmt.Printf("Email From: %s\n", cfg.EmailFrom)
	fmt.Printf("Stripe API Key set: %v\n", cfg.StripeSecretKey != "")
	fmt.Println("==== END CONFIGURATION ====\n")

	// Initialize Firebase service
	firebaseService, err := services.NewFirebaseService(cfg.FirebaseProjectID, cfg.GoogleApplicationCredentials)
	if err != nil {
		log.Fatalf("Failed to initialize Firebase service: %v", err)
	}

	// Initialize Email service
	emailService, err := services.NewEmailService(cfg.ResendAPIKey, cfg.EmailFrom)
	if err != nil {
		log.Fatalf("Failed to initialize Email service: %v", err)
	}

	// Initialize Stripe service
	stripeService := services.NewStripeService()

	// Initialize handlers
	authHandler := handlers.NewAuthHandler(firebaseService)
	memberHandler := handlers.NewMemberHandler(firebaseService, emailService)
	classHandler := handlers.NewClassHandler(firebaseService)
	dashboardHandler := handlers.NewDashboardHandler(firebaseService)
	gymHandler := handlers.NewGymHandler(firebaseService)
	membershipHandler := handlers.NewMembershipHandler(firebaseService)
	bookingHandler := handlers.NewBookingHandler(firebaseService)
	paymentHandler := handlers.NewPaymentHandler(firebaseService, stripeService) // Add payment handler

	// Setup Gin router
	router := setupRouter(cfg, firebaseService, authHandler, memberHandler, classHandler, dashboardHandler, gymHandler, membershipHandler, bookingHandler, paymentHandler)

	// Create HTTP server
	server := &http.Server{
		Addr:    ":" + cfg.Port,
		Handler: router,
	}

	// Start server in a goroutine
	go func() {
		log.Printf("Server starting on port %s", cfg.Port)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Println("Shutting down server...")

	// Create a context with timeout for shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Attempt graceful shutdown
	if err := server.Shutdown(ctx); err != nil {
		log.Fatalf("Server forced to shutdown: %v", err)
	}
	log.Println("Server exited")
}

// setupRouter configures and returns the Gin router with all routes
func setupRouter(cfg *config.Config, firebaseService *services.FirebaseService, authHandler *handlers.AuthHandler, memberHandler *handlers.MemberHandler, classHandler *handlers.ClassHandler, dashboardHandler *handlers.DashboardHandler, gymHandler *handlers.GymHandler, membershipHandler *handlers.MembershipHandler, bookingHandler *handlers.BookingHandler, paymentHandler *handlers.PaymentHandler) *gin.Engine {
	// Set Gin mode based on environment
	if cfg.Environment == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.Default()

	// CORS configuration
	corsConfig := cors.DefaultConfig()
	// Update CORS configuration to allow multiple origins
	corsConfig.AllowOrigins = []string{
		cfg.CORSOrigin,
		"http://localhost:5173",
		"http://*************:5173", // Add your local IP address
	}
	corsConfig.AllowMethods = []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"}
	corsConfig.AllowHeaders = []string{"Origin", "Content-Type", "Accept", "Authorization"}
	corsConfig.AllowCredentials = true
	router.Use(cors.New(corsConfig))

	// Health check endpoint
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":    "healthy",
			"timestamp": time.Now().Unix(),
		})
	})

	// API version 1 routes
	v1 := router.Group("/api/v1")
	{
		// Authentication routes (public)
		auth := v1.Group("/auth")
		{
			auth.POST("/verify", authHandler.VerifyToken)
			auth.POST("/refresh", authHandler.RefreshToken)
			auth.POST("/verify-invitation", authHandler.VerifyInvitation)
			auth.POST("/link-member-account", authHandler.LinkMemberAccount)
		}

		// Stripe webhook endpoint (public)
		v1.POST("/webhook", paymentHandler.HandleWebhook)

		// Protected routes - require authentication
		protected := v1.Group("/")
		authMiddleware := middleware.AuthMiddleware(firebaseService)
		protected.Use(authMiddleware)
		{
			// Dashboard routes
			protected.GET("/dashboard/stats", dashboardHandler.GetBasicStats)

			// Gym management routes
			gym := protected.Group("/gym")
			{
				gym.GET("", gymHandler.GetGymInfo)
				gym.PUT("", gymHandler.UpdateGymInfo)
			}

			// Member management routes
			members := protected.Group("/members")
			{
				members.GET("", memberHandler.GetAllMembers)
				members.GET("/:id", memberHandler.GetMemberByID)
				members.POST("", memberHandler.CreateMember)
				members.PUT("/:id", memberHandler.UpdateMember)
				members.DELETE("/:id", memberHandler.DeleteMember)
				members.POST("/:id/invite", memberHandler.SendMemberInvitation)
			}

			// Class management routes
			classes := protected.Group("/classes")
			{
				classes.GET("", classHandler.GetAllClasses)
				classes.GET("/:id", classHandler.GetClassByID)
				classes.POST("", classHandler.CreateClass)
				classes.PUT("/:id", classHandler.UpdateClass)
				classes.DELETE("/:id", classHandler.DeleteClass)

				// Class instances routes
				classes.GET("/instances", classHandler.GetClassInstances)
				classes.POST("/generate-instances", classHandler.GenerateClassInstances)
				classes.PUT("/instances/:id", classHandler.UpdateClassInstance)
				classes.PUT("/instances/:id/cancel", classHandler.CancelClassInstance)
			}

			// Insights routes
			insights := protected.Group("/insights")
			{
				insights.GET("", dashboardHandler.GetAllInsights) // Single comprehensive endpoint
				// Legacy endpoints (kept for backward compatibility)
				insights.GET("/new-members", dashboardHandler.GetNewMembersThisMonth)
				insights.GET("/member-growth", dashboardHandler.GetMemberGrowthStats)
			}

			// Membership plan routes
			memberships := protected.Group("/memberships")
			{
				memberships.GET("", membershipHandler.GetAllMembershipPlans)
				memberships.GET("/:id", membershipHandler.GetMembershipPlanByID)
				memberships.POST("", membershipHandler.CreateMembershipPlan)
				memberships.PUT("/:id", membershipHandler.UpdateMembershipPlan)
				memberships.DELETE("/:id", membershipHandler.DeleteMembershipPlan)
			}

			// Booking routes
			bookings := protected.Group("/bookings")
			{
				bookings.GET("", bookingHandler.GetAllBookingsForUser)
				bookings.GET("/:id", bookingHandler.GetBookingByID)
				bookings.POST("", bookingHandler.CreateBooking)
				bookings.DELETE("/:id", bookingHandler.DeleteBooking)
			}

			// Payment routes
			payments := protected.Group("/payments")
			{
				payments.POST("/create-checkout-session", paymentHandler.CreateCheckoutSession)
			}
		}
	}

	return router
}

// Helper function to mask API key for logging
func maskAPIKey(key string) string {
	if len(key) <= 8 {
		return "***"
	}
	return key[:4] + "..." + key[len(key)-4:]
}
