# Firebase Emulator Seeding

This document explains how to seed your Firebase local emulator with test data for development.

## Overview

The seeding system creates realistic test data including:
- Test gym with basic information
- Sample users (coaches and admins)
- Weekly class templates (Kara<PERSON>, BJJ, Muay Thai, MMA, Workshops)
- Sample members with emergency contacts
- Membership plans (Basic, Premium, Family)
- Generated class instances for the next 30 days

## Quick Start

### Option 1: Using the development setup script (Recommended)

```bash
# Make the script executable (first time only)
chmod +x scripts/dev-setup.sh

# Start emulators and seed data in one command
./scripts/dev-setup.sh
```

### Option 2: Manual setup

```bash
# 1. Start the Firebase emulators
make emulators

# 2. In a new terminal, seed the database
make seed

# 3. Start the backend server
make run
```

## Available Commands

```bash
# Start Firebase emulators (Firestore + Auth)
make emulators

# Seed the emulator with test data
make seed

# Run the backend server
make run

# Stop emulators (Ctrl+C in emulator terminal)
make stop

# Clear all emulator data
make clean

# Show all available commands
make help
```

## Test Data Created

### Gym
- **Name**: DojoFy Test Gym
- **Address**: 123 Martial Arts Way, Test City, TC 12345
- **Contact**: <EMAIL>, +1-555-0123

### Users
- **Coach John** (<EMAIL>) - Coach role
- **Admin User** (<EMAIL>) - Admin role

### Weekly Classes
1. **Beginner Karate** - Mondays 18:00-19:00 (20 max)
2. **Advanced BJJ** - Tuesdays 19:00-20:30 (15 max)
3. **Muay Thai Basics** - Wednesdays 17:30-18:30 (25 max)
4. **Mixed Martial Arts** - Thursdays 19:30-21:00 (18 max)
5. **Weekend Workshop** - Saturdays 10:00-12:00 (30 max)

### Members
- **Alice Johnson** - Basic Plan member (joined 2 months ago)
- **Charlie Brown** - Premium Plan member (joined 1 month ago)
- **Eva Martinez** - Basic Plan member (joined 15 days ago)

### Membership Plans
- **Basic Plan** - $49.99/month, 8 classes
- **Premium Plan** - $89.99/month, unlimited classes
- **Family Plan** - $149.99/month, unlimited classes for up to 4 members

### Class Instances
- Automatically generates class instances for the next 30 days
- Each instance is scheduled based on the weekly class template
- All instances start with 0 enrolled students

## Emulator URLs

- **Firestore Emulator**: http://localhost:8081
- **Auth Emulator**: http://localhost:9099

## Customizing Seed Data

To modify the test data, edit the following functions in `cmd/seed/main.go`:

- `createTestGym()` - Modify gym information
- `createTestUsers()` - Add/modify users
- `createTestClasses()` - Add/modify weekly class templates
- `createTestMembers()` - Add/modify members
- `createTestMemberships()` - Add/modify membership plans

## Troubleshooting

### Emulator not starting
```bash
# Check if Firebase CLI is installed
firebase --version

# Install Firebase CLI if needed
npm install -g firebase-tools
```

### Seed command fails
```bash
# Make sure emulators are running first
make emulators

# Check emulator logs for errors
# The emulator terminal will show detailed error messages
```

### Port conflicts
If ports 8081 or 9099 are already in use:
```bash
# Kill processes using those ports
lsof -ti:8081 | xargs kill -9
lsof -ti:9099 | xargs kill -9

# Or modify the ports in the Makefile
```

### Clear all data and start fresh
```bash
make clean
make emulators
make seed
```

## Environment Variables

The seeding system uses these environment variables:
- `USE_FIREBASE_EMULATORS=true` - Enables emulator mode
- `FIREBASE_PROJECT_ID=demo-project` - Project ID for emulators
- `FIRESTORE_EMULATOR_HOST=localhost:8081` - Firestore emulator host
- `FIREBASE_AUTH_EMULATOR_HOST=localhost:9099` - Auth emulator host

These are automatically set by the Makefile when using the provided commands. 