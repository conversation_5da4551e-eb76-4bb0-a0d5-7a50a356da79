# Makefile for local backend development with Firebase emulators

EMULATOR_PROJECT_ID=demo-project
EMULATOR_FIRESTORE_PORT=8085
EMULATOR_AUTH_PORT=9099

export USE_FIREBASE_EMULATORS=true
export FIREBASE_PROJECT_ID=$(EMULATOR_PROJECT_ID)
export FIRESTORE_EMULATOR_HOST=localhost:$(EMULATOR_FIRESTORE_PORT)
export FIREBASE_AUTH_EMULATOR_HOST=localhost:$(EMULATOR_AUTH_PORT)

.PHONY: emulators run stop clean help seed migrate check

help:
	@echo "Available targets:"
	@echo "  emulators   Start Firebase Emulator Suite (Firestore, Auth)"
	@echo "  run         Run the backend server using emulators"
	@echo "  seed        Seed the emulator with test data"
	@echo "  migrate     Run migration to fix class_instances time fields"
	@echo "  check       Check specific class instance document"
	@echo "  stop        Stop emulators (Ctrl+C in emulator terminal)"
	@echo "  clean       Remove local emulator data (emulator-data/)"

emulators:
	@echo "Starting Firebase Emulator Suite (Firestore, Auth)..."
	firebase emulators:start --only firestore,auth --project $(EMULATOR_PROJECT_ID) --import=emulator-data --export-on-exit=emulator-data --config="../firebase/firebase.json"

run:
	@echo "Running backend with Firebase emulators..."
	go run ./cmd/server/main.go

seed:
	@echo "Seeding Firebase emulator with test data..."
	go run ./cmd/seed/main.go

migrate:
	@echo "Running migration to fix class_instances time fields..."
	go run ./cmd/migrate/main.go

check:
	@echo "Checking specific class instance document..."
	go run ./cmd/check/main.go

stop:
	@echo "To stop emulators, press Ctrl+C in the emulator terminal."

clean:
	rm -rf emulator-data 